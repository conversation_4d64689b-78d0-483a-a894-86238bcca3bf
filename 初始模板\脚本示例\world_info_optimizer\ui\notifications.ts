// src/world_info_optimizer/ui/notifications.ts

/**
 * 统一的Toast通知系统
 * 基于SweetAlert2实现，提供多种类型的非阻塞用户反馈
 */

export type ToastType = 'success' | 'error' | 'warning' | 'info';
export type ToastPosition =
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'center'
  | 'center-start'
  | 'center-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end';

export interface ToastOptions {
  title: string;
  type?: ToastType;
  position?: ToastPosition;
  duration?: number;
  showProgress?: boolean;
  allowClose?: boolean;
}

export interface ProgressToastController {
  update: (message: string) => void;
  remove: () => void;
}

/**
 * 获取SweetAlert2实例
 */
const getSwal = () => {
  const Swal = window.parent.Swal;
  if (!Swal) {
    console.warn('[WIO Notifications] SweetAlert2 not available, falling back to console');
    return null;
  }
  return Swal;
};

/**
 * 显示Toast通知
 * @param options Toast配置选项
 */
export const showToast = (options: ToastOptions): void => {
  const {
    title,
    type = 'info',
    position = 'top-end',
    duration = 3000,
    showProgress = true,
    allowClose = false,
  } = options;

  const Swal = getSwal();
  if (!Swal) {
    // 降级到控制台输出
    const prefix = `[${type.toUpperCase()}]`;
    console.log(`${prefix} ${title}`);
    return;
  }

  const Toast = Swal.mixin({
    toast: true,
    position,
    showConfirmButton: allowClose,
    timer: duration,
    timerProgressBar: showProgress,
    didOpen: (toast: HTMLElement) => {
      if (showProgress) {
        toast.addEventListener('mouseenter', Swal.stopTimer);
        toast.addEventListener('mouseleave', Swal.resumeTimer);
      }
    },
  });

  Toast.fire({
    icon: type,
    title,
  });
};

/**
 * 显示成功Toast
 * @param message 成功消息
 * @param duration 显示时长（毫秒）
 */
export const showSuccessToast = (message: string = '操作成功', duration: number = 1500): void => {
  showToast({
    title: message,
    type: 'success',
    duration,
    position: 'top-end',
  });
};

/**
 * 显示错误Toast
 * @param message 错误消息
 * @param duration 显示时长（毫秒）
 */
export const showErrorToast = (message: string, duration: number = 4000): void => {
  showToast({
    title: message,
    type: 'error',
    duration,
    position: 'top-end',
  });
};

/**
 * 显示警告Toast
 * @param message 警告消息
 * @param duration 显示时长（毫秒）
 */
export const showWarningToast = (message: string, duration: number = 3000): void => {
  showToast({
    title: message,
    type: 'warning',
    duration,
    position: 'top-end',
  });
};

/**
 * 显示信息Toast
 * @param message 信息消息
 * @param duration 显示时长（毫秒）
 */
export const showInfoToast = (message: string, duration: number = 3000): void => {
  showToast({
    title: message,
    type: 'info',
    duration,
    position: 'top-end',
  });
};

/**
 * 显示进度Toast（用于长时间操作）
 * @param initialMessage 初始消息
 * @returns 进度控制器，可用于更新消息或关闭Toast
 */
export const showProgressToast = (initialMessage: string = '正在处理...'): ProgressToastController => {
  const Swal = getSwal();
  if (!Swal) {
    // 降级到控制台输出
    console.log(`[PROGRESS] ${initialMessage}`);
    return {
      update: (newMessage: string) => console.log(`[PROGRESS UPDATE] ${newMessage}`),
      remove: () => console.log(`[PROGRESS] Done.`),
    };
  }

  Swal.fire({
    toast: true,
    position: 'bottom-end',
    title: initialMessage,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    },
  });

  return {
    update: (newMessage: string) => {
      Swal.update({
        title: newMessage,
      });
    },
    remove: () => {
      Swal.close();
    },
  };
};

// === 向后兼容性支持 ===

/**
 * @deprecated 使用 showSuccessToast 替代
 * 显示成功提示（向后兼容）
 */
export const showSuccessTick = (message: string = '操作成功', duration: number = 1500): void => {
  showSuccessToast(message, duration);
};

// === 便捷函数 ===

/**
 * 显示操作成功的Toast
 * @param operation 操作名称
 * @param target 操作目标（可选）
 */
export const showOperationSuccess = (operation: string, target?: string): void => {
  const message = target ? `${operation} "${target}" 成功` : `${operation}成功`;
  showSuccessToast(message);
};

/**
 * 显示操作失败的Toast
 * @param operation 操作名称
 * @param error 错误信息
 * @param target 操作目标（可选）
 */
export const showOperationError = (operation: string, error: string, target?: string): void => {
  const message = target ? `${operation} "${target}" 失败: ${error}` : `${operation}失败: ${error}`;
  showErrorToast(message);
};

/**
 * 显示批量操作结果的Toast
 * @param operation 操作名称
 * @param successCount 成功数量
 * @param totalCount 总数量
 */
export const showBulkOperationResult = (operation: string, successCount: number, totalCount: number): void => {
  if (successCount === totalCount) {
    showSuccessToast(`批量${operation}完成：${successCount}/${totalCount} 项成功`);
  } else if (successCount > 0) {
    showWarningToast(`批量${operation}部分完成：${successCount}/${totalCount} 项成功`);
  } else {
    showErrorToast(`批量${operation}失败：0/${totalCount} 项成功`);
  }
};
