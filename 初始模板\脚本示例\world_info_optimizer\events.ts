// src/world_info_optimizer/events.ts

import {
  BU<PERSON>K_DELETE_BTN_ID,
  BULK_DISABLE_BTN_ID,
  BULK_ENABLE_BTN_ID,
  BULK_REPLACE_BTN_ID,
  BULK_REPLACE_INPUT_ID,
  BUL<PERSON>_SEARCH_INPUT_ID,
  BUTTON_ID,
  COLLAPSE_ALL_BTN_ID,
  COLLAPSE_CURRENT_BTN_ID,
  CREATE_LOREBOOK_BTN_ID,
  EXPAND_ALL_BTN_ID,
  MULTI_SELECT_BTN_ID,
  PANEL_ID,
  REFRESH_BTN_ID,
  REFRESH_CHARACTER_BTN_ID,
  SEARCH_INPUT_ID,
} from './constants';
import {
  createLorebook,
  createLorebookEntry,
  createRegex,
  deleteLorebook,
  deleteLorebookEntry,
  deleteRegex,
  loadAllData,
  performBulkDelete,
  performBulkDisable,
  performBulkEnable,
  performBulkReplace,
  refreshCharacterD<PERSON>,
  renameLorebook,
  setGlobalLorebookEnabled,
  updateLorebookEntry,
  updateRegex,
} from './core';
import {
  collapseAllBooks,
  collapseCurrentCharacterBooks,
  expandAllBooks,
  getState,
  setActiveTab,
  setSearchQuery,
  toggleBookCollapse,
  toggleMultiSelectMode,
} from './store';
import { showEntryEditorModal, showModal, showRegexEditorModal } from './ui/modals';

let parentDoc: Document;
let $: JQueryStatic;

/**
 * 初始化所有UI事件处理器。
 * 这个函数应该在UI注入DOM后调用。
 * @param parentWindow 父窗口对象
 */
export const initializeEventHandlers = (parentWindow: Window) => {
  parentDoc = parentWindow.document;
  $ = parentWindow.jQuery;
  const $body = $('body', parentDoc);

  // --- 主面板和按钮的事件 ---

  // 打开面板的按钮
  $body.on('click', `#${BUTTON_ID}`, () => {
    $(`#${PANEL_ID}`, parentDoc).fadeIn(200);
    // 首次打开时加载数据
    if (!getState().isDataLoaded) {
      loadAllData();
    }
  });

  // 关闭面板的按钮
  $body.on('click', `#${PANEL_ID} .wio-close-btn`, () => {
    $(`#${PANEL_ID}`, parentDoc).fadeOut(200);
  });

  // --- 事件委托：主面板内的所有点击事件 ---
  const $panel = $(`#${PANEL_ID}`, parentDoc);

  $panel.on('click', async event => {
    const $target = $(event.target);

    // --- 标签页切换 ---
    const tabButton = $target.closest('.wio-tab-btn');
    if (tabButton.length) {
      const tabId = tabButton.data('tab-id');
      setActiveTab(tabId);
      return;
    }

    // --- 工具栏操作 ---
    if ($target.closest(`#${CREATE_LOREBOOK_BTN_ID}`).length) {
      handleCreateBook();
      return;
    }
    if ($target.closest(`#${REFRESH_BTN_ID}`).length || $target.closest(`#${REFRESH_BTN_ID}-retry`).length) {
      loadAllData();
      return;
    }
    if ($target.closest(`#${REFRESH_CHARACTER_BTN_ID}`).length) {
      refreshCharacterData();
      return;
    }

    // --- 批量操作 ---
    if ($target.closest(`#${BULK_REPLACE_BTN_ID}`).length) {
      handleBulkReplace();
      return;
    }

    // --- 多选模式 ---
    if ($target.closest(`#${MULTI_SELECT_BTN_ID}`).length) {
      toggleMultiSelectMode();
      return;
    }

    // --- 批量操作按钮 ---
    if ($target.closest(`#${BULK_DELETE_BTN_ID}`).length) {
      handleBulkDelete();
      return;
    }
    if ($target.closest(`#${BULK_ENABLE_BTN_ID}`).length) {
      handleBulkEnable();
      return;
    }
    if ($target.closest(`#${BULK_DISABLE_BTN_ID}`).length) {
      handleBulkDisable();
      return;
    }

    // --- 折叠/展开操作 ---
    if ($target.closest(`#${COLLAPSE_CURRENT_BTN_ID}`).length) {
      collapseCurrentCharacterBooks();
      return;
    }
    if ($target.closest(`#${COLLAPSE_ALL_BTN_ID}`).length) {
      collapseAllBooks();
      return;
    }
    if ($target.closest(`#${EXPAND_ALL_BTN_ID}`).length) {
      expandAllBooks();
      return;
    }

    // --- 世界书折叠切换 ---
    const collapseToggle = $target.closest('.wio-collapse-toggle');
    if (collapseToggle.length) {
      const bookName = collapseToggle.closest('.wio-book-group').data('book-name');
      toggleBookCollapse(bookName);
      return;
    }

    // --- 世界书操作 ---
    const renameBookBtn = $target.closest('.wio-rename-book-btn');
    if (renameBookBtn.length) {
      const bookName = renameBookBtn.closest('.wio-book-group').data('book-name');
      handleRenameBook(bookName);
      return;
    }

    const deleteBookBtn = $target.closest('.wio-delete-book-btn');
    if (deleteBookBtn.length) {
      const bookName = deleteBookBtn.closest('.wio-book-group').data('book-name');
      handleDeleteBook(bookName);
      return;
    }

    // --- 条目操作 ---
    const createEntryBtn = $target.closest('.wio-create-entry-btn');
    if (createEntryBtn.length) {
      const bookName = createEntryBtn.data('book-name');
      handleCreateEntry(bookName);
      return;
    }

    const editEntryBtn = $target.closest('.wio-edit-entry-btn');
    if (editEntryBtn.length) {
      const entryItem = editEntryBtn.closest('.wio-entry-item');
      const bookName = entryItem.data('book-name');
      const uid = entryItem.data('uid');
      handleEditEntry(bookName, uid);
      return;
    }

    const deleteEntryBtn = $target.closest('.wio-delete-entry-btn');
    if (deleteEntryBtn.length) {
      const entryItem = deleteEntryBtn.closest('.wio-entry-item');
      const bookName = entryItem.data('book-name');
      const uid = entryItem.data('uid');
      handleDeleteEntry(bookName, uid);
      return;
    }

    // --- 正则操作 ---
    const createRegexBtn = $target.closest('.wio-create-regex-btn');
    if (createRegexBtn.length) {
      const scope = createRegexBtn.data('scope');
      handleCreateRegex(scope);
      return;
    }

    const editRegexBtn = $target.closest('.wio-edit-regex-btn');
    if (editRegexBtn.length) {
      const regexItem = editRegexBtn.closest('.wio-regex-item');
      const regexId = regexItem.data('id');
      handleEditRegex(regexId);
      return;
    }

    const deleteRegexBtn = $target.closest('.wio-delete-regex-btn');
    if (deleteRegexBtn.length) {
      const regexItem = deleteRegexBtn.closest('.wio-regex-item');
      const regexId = regexItem.data('id');
      handleDeleteRegex(regexId);
      return;
    }
  });

  // --- 事件委托：处理表单元素的 change 事件 ---
  $panel.on('change', async event => {
    const $target = $(event.target);

    // --- 全局世界书启用/禁用切换 ---
    if ($target.is('.wio-global-book-toggle')) {
      const bookName = $target.closest('.wio-book-group').data('book-name');
      const isEnabled = $target.prop('checked');
      await setGlobalLorebookEnabled(bookName, isEnabled);
      return;
    }

    // --- 条目启用/禁用切换 ---
    if ($target.is('.wio-entry-toggle')) {
      const entryItem = $target.closest('.wio-entry-item');
      const bookName = entryItem.data('book-name');
      const uid = entryItem.data('uid');
      const isEnabled = $target.prop('checked');
      await updateLorebookEntry(bookName, uid, { enabled: isEnabled });
      return;
    }

    // --- 正则启用/禁用切换 ---
    if ($target.is('.wio-regex-toggle')) {
      const regexItem = $target.closest('.wio-regex-item');
      const regexId = regexItem.data('id');
      const isEnabled = $target.prop('checked');
      await updateRegex(regexId, { enabled: isEnabled });
      return;
    }
  });

  // --- 事件委托：处理搜索框输入 ---
  $panel.on('input', `#${SEARCH_INPUT_ID}`, event => {
    const query = $(event.target).val() as string;
    setSearchQuery(query);
  });
};

// --- 事件处理器具体实现 ---

/**
 * 处理重命名世界书的逻辑。
 * @param bookName 要重命名的世界书的当前名称
 */
const handleRenameBook = async (bookName: string) => {
  try {
    const newName = await showModal({
      type: 'prompt',
      title: '重命名世界书',
      text: `为 "${bookName}" 输入新的名称:`,
      value: bookName,
    });

    if (typeof newName === 'string' && newName.trim() && newName !== bookName) {
      await renameLorebook(bookName, newName.trim());
    }
  } catch (error) {
    // 用户取消了输入
    console.log('Rename operation cancelled.');
  }
};

/**
 * 处理删除世界书的逻辑。
 * @param bookName 要删除的世界书的名称
 */
const handleDeleteBook = async (bookName: string) => {
  try {
    const confirmation = await showModal({
      type: 'confirm',
      title: '确认删除',
      text: `你确定要永久删除世界书 "${bookName}" 吗？此操作无法撤销。`,
    });

    if (confirmation) {
      await deleteLorebook(bookName);
    }
  } catch (error) {
    // 用户取消了确认
    console.log('Delete operation cancelled.');
  }
};

/**
 * 处理创建新条目的逻辑。
 * @param bookName 新条目所属的世界书名称
 */
const handleCreateEntry = async (bookName: string) => {
  try {
    const newEntryData = await showEntryEditorModal({
      entry: { keys: [], content: '', comment: '' },
      bookName,
      isCreating: true,
    });

    // showEntryEditorModal 返回的 newEntryData 不包含 enabled 状态, 我们需要设置默认值
    const entryToCreate = {
      ...newEntryData,
      enabled: true, // 新条目默认启用
    };

    await createLorebookEntry(bookName, entryToCreate);
  } catch (error) {
    console.log('Create entry operation cancelled.');
  }
};

/**
 * 处理编辑现有条目的逻辑。
 * @param bookName 条目所属的世界书名称
 * @param uid 要编辑的条目的UID
 */
const handleEditEntry = async (bookName: string, uid: string) => {
  const state = getState();
  const entry = state.lorebookEntries.get(bookName)?.find(e => e.uid === uid);

  if (!entry) {
    console.error(`Entry with UID ${uid} not found in book ${bookName}.`);
    return;
  }

  try {
    const updatedEntryData = await showEntryEditorModal({
      entry: { ...entry }, // 传入副本以防意外修改
      bookName,
      isCreating: false,
    });

    // 我们只需要更新发生变化的部分
    const updates = {
      comment: updatedEntryData.comment,
      keys: updatedEntryData.keys,
      content: updatedEntryData.content,
    };

    await updateLorebookEntry(bookName, uid, updates);
  } catch (error) {
    console.log('Edit entry operation cancelled.');
  }
};

/**
 * 处理删除条目的逻辑。
 * @param bookName 条目所属的世界书名称
 * @param uid 要删除的条目的UID
 */
const handleDeleteEntry = async (bookName: string, uid: string) => {
  try {
    const confirmation = await showModal({
      type: 'confirm',
      title: '确认删除',
      text: `你确定要永久删除这个条目吗？`,
    });

    if (confirmation) {
      await deleteLorebookEntry(bookName, uid);
    }
  } catch (error) {
    console.log('Delete entry operation cancelled.');
  }
};

/**
 * 处理创建新世界书的逻辑。
 */
const handleCreateBook = async () => {
  try {
    const newName = await showModal({
      type: 'prompt',
      title: '创建新世界书',
      text: '请输入新世界书的名称:',
      value: 'New-Lorebook',
    });

    if (typeof newName === 'string' && newName.trim()) {
      await createLorebook(newName.trim());
    }
  } catch (error) {
    console.log('Create lorebook operation cancelled.');
  }
};

// --- 正则表达式事件处理器 ---

const handleCreateRegex = async (scope: 'global' | 'character') => {
  try {
    const newRegexData = await showRegexEditorModal({
      regex: { script_name: '新正则', find_regex: '', replace_string: '' },
      isCreating: true,
    });
    await createRegex({ ...newRegexData, scope });
  } catch (error) {
    console.log('Create regex operation cancelled.');
  }
};

const handleEditRegex = async (regexId: string) => {
  const state = getState();
  const allRegexes = [...state.regexes.global, ...state.regexes.character];
  const regex = allRegexes.find(r => r.id === regexId);

  if (!regex) {
    console.error(`Regex with ID ${regexId} not found.`);
    return;
  }
  // 卡片内正则不可编辑
  if (regex.source === 'card') {
    await showModal({ type: 'alert', title: '操作无效', text: '无法编辑来自角色卡的正则表达式。' });
    return;
  }

  try {
    const updatedRegexData = await showRegexEditorModal({
      regex: { ...regex },
      isCreating: false,
    });
    await updateRegex(regexId, updatedRegexData);
  } catch (error) {
    console.log('Edit regex operation cancelled.');
  }
};

const handleDeleteRegex = async (regexId: string) => {
  const state = getState();
  const allRegexes = [...state.regexes.global, ...state.regexes.character];
  const regex = allRegexes.find(r => r.id === regexId);

  if (regex && regex.source === 'card') {
    await showModal({ type: 'alert', title: '操作无效', text: '无法删除来自角色卡的正则表达式。' });
    return;
  }

  try {
    const confirmation = await showModal({
      type: 'confirm',
      title: '确认删除',
      text: '你确定要永久删除这个正则表达式吗？',
    });
    if (confirmation) {
      await deleteRegex(regexId);
    }
  } catch (error) {
    console.log('Delete regex operation cancelled.');
  }
};

// --- 批量操作事件处理器 ---

/**
 * 处理批量搜索替换操作。
 */
const handleBulkReplace = async () => {
  const searchInput = document.getElementById(BULK_SEARCH_INPUT_ID) as HTMLInputElement;
  const replaceInput = document.getElementById(BULK_REPLACE_INPUT_ID) as HTMLInputElement;

  if (!searchInput || !replaceInput) {
    console.error('Bulk replace inputs not found');
    return;
  }

  const searchTerm = searchInput.value.trim();
  const replaceTerm = replaceInput.value.trim();

  if (!searchTerm) {
    await showModal({
      type: 'alert',
      title: '输入错误',
      text: '请输入要搜索的内容。',
    });
    return;
  }

  try {
    // 显示确认对话框
    const confirmation = await showModal({
      type: 'confirm',
      title: '确认批量替换',
      text: `你确定要将所有世界书条目中的 "${searchTerm}" 替换为 "${replaceTerm}" 吗？\n\n此操作将影响所有匹配的条目名称、关键词和内容。`,
    });

    if (confirmation) {
      const modifiedCount = await performBulkReplace(searchTerm, replaceTerm);
      await showModal({
        type: 'alert',
        title: '替换完成',
        text: `成功替换了 ${modifiedCount} 个条目中的内容。`,
      });

      // 清空输入框
      searchInput.value = '';
      replaceInput.value = '';
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    await showModal({
      type: 'alert',
      title: '替换失败',
      text: `批量替换操作失败: ${errorMessage}`,
    });
  }
};

/**
 * 处理批量删除操作。
 */
const handleBulkDelete = async () => {
  const state = getState();
  const selectedCount = state.selectedItems.size;

  if (selectedCount === 0) {
    await showModal({
      type: 'alert',
      title: '没有选中项',
      text: '请先选择要删除的条目。',
    });
    return;
  }

  try {
    const confirmation = await showModal({
      type: 'confirm',
      title: '确认批量删除',
      text: `你确定要永久删除选中的 ${selectedCount} 个条目吗？此操作无法撤销。`,
    });

    if (confirmation) {
      const deletedCount = await performBulkDelete();
      await showModal({
        type: 'alert',
        title: '删除完成',
        text: `成功删除了 ${deletedCount} 个条目。`,
      });
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    await showModal({
      type: 'alert',
      title: '删除失败',
      text: `批量删除操作失败: ${errorMessage}`,
    });
  }
};

/**
 * 处理批量启用操作。
 */
const handleBulkEnable = async () => {
  const state = getState();
  const selectedCount = state.selectedItems.size;

  if (selectedCount === 0) {
    await showModal({
      type: 'alert',
      title: '没有选中项',
      text: '请先选择要启用的条目。',
    });
    return;
  }

  try {
    const modifiedCount = await performBulkEnable();
    await showModal({
      type: 'alert',
      title: '启用完成',
      text: `成功启用了 ${modifiedCount} 个条目。`,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    await showModal({
      type: 'alert',
      title: '启用失败',
      text: `批量启用操作失败: ${errorMessage}`,
    });
  }
};

/**
 * 处理批量禁用操作。
 */
const handleBulkDisable = async () => {
  const state = getState();
  const selectedCount = state.selectedItems.size;

  if (selectedCount === 0) {
    await showModal({
      type: 'alert',
      title: '没有选中项',
      text: '请先选择要禁用的条目。',
    });
    return;
  }

  try {
    const modifiedCount = await performBulkDisable();
    await showModal({
      type: 'alert',
      title: '禁用完成',
      text: `成功禁用了 ${modifiedCount} 个条目。`,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    await showModal({
      type: 'alert',
      title: '禁用失败',
      text: `批量禁用操作失败: ${errorMessage}`,
    });
  }
};
