// src/world_info_optimizer/test-phase1.ts
// 测试Phase 1功能的简单测试文件

import { getState, updateState, toggleMultiSelectMode, toggleItemSelection, toggleBookCollapse } from './store';
import { performBulkReplace, performBulkDelete, performBulkEnable, performBulkDisable } from './core';

/**
 * 测试状态管理功能
 */
export const testStateManagement = () => {
  console.log('=== 测试状态管理功能 ===');

  // 测试多选模式切换
  console.log('初始多选模式:', getState().multiSelectMode);
  toggleMultiSelectMode();
  console.log('切换后多选模式:', getState().multiSelectMode);

  // 测试项目选择
  toggleItemSelection('test-book/test-entry-1');
  toggleItemSelection('test-book/test-entry-2');
  console.log('选中的项目:', Array.from(getState().selectedItems));

  // 测试世界书折叠
  toggleBookCollapse('test-book');
  console.log('折叠的世界书:', Array.from(getState().collapsedBooks));

  console.log('状态管理测试完成\n');
};

/**
 * 测试批量操作功能（模拟）
 */
export const testBulkOperations = async () => {
  console.log('=== 测试批量操作功能 ===');

  try {
    // 注意：这些函数在没有真实数据的情况下会抛出错误
    // 这里只是测试函数是否正确导入和调用

    console.log('测试批量替换函数导入:', typeof performBulkReplace === 'function');
    console.log('测试批量删除函数导入:', typeof performBulkDelete === 'function');
    console.log('测试批量启用函数导入:', typeof performBulkEnable === 'function');
    console.log('测试批量禁用函数导入:', typeof performBulkDisable === 'function');

    // 测试空搜索词的错误处理
    try {
      await performBulkReplace('', 'test');
    } catch (error) {
      console.log('正确捕获空搜索词错误:', error.message);
    }

    // 测试没有选中项的错误处理
    try {
      await performBulkDelete();
    } catch (error) {
      console.log('正确捕获没有选中项错误:', error.message);
    }
  } catch (error) {
    console.error('批量操作测试出错:', error);
  }

  console.log('批量操作测试完成\n');
};

/**
 * 运行所有测试
 */
export const runPhase1Tests = async () => {
  console.log('开始Phase 1功能测试...\n');

  testStateManagement();
  await testBulkOperations();

  console.log('Phase 1功能测试完成！');
};

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runPhase1Tests().catch(console.error);
}
