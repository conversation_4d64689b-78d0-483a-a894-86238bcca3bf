import { createApp } from 'vue';
import { createMemoryHistory, createRouter } from 'vue-router';
import { createPinia } from 'pinia';
import App from '../app.vue'; // 调整 App 的引用路径
import RegexView from '../../views/RegexView.vue'; // 引入 Regex 视图
import CharacterLorebookView from '../../views/CharacterLorebookView.vue'; // 引入新视图

const router = createRouter({
  history: createMemoryHistory(),
  routes: [
    { path: '/', component: CharacterLorebookView }, // 设置为默认路由
    { path: '/regex', component: RegexView },
    { path: '/lorebook', component: CharacterLorebookView },
  ],
});
router.replace('/');

const pinia = createPinia();

$(() => {
  createApp(App).use(pinia).use(router).mount('#app');
});
