// SillyTavern 内置的世界信息（World Info）相关类型，作为参考
// 源自：@types/iframe/exported.sillytavern.d.ts

/**
 * @type {SillyTavern.v2WorldInfoBook}
 */
interface Worldbook {
  name: string;
  entries: WorldbookEntry[];
  // ... 其他可能的字段
}

/**
 * @type {SillyTavern.v2DataWorldInfoEntry}
 */
interface WorldbookEntry {
  keys: string[];
  content: string;
  enabled: boolean;
  comment: string;
  secondary_keys: string[];
  constant: boolean;
  selective: boolean;
  insertion_order: number;
  position: 'before_char' | 'after_char';
  id: number;
  extensions: object;
  // ... 其他可能的字段
}

// -------------------------------------------------------------------------
// BridgeAPI 定义开始
// -------------------------------------------------------------------------

/**
 * 为 `getWorldbookList()` 方法返回的轻量级对象，专为列表页性能优化设计。
 */
export interface WorldbookListItem {
  name: string;
  entryCount: number;
}

/**
 * 自定义错误类，用于封装 BridgeAPI 中发生的错误。
 */
export class BridgeError extends Error {
  constructor(
    public code: string,
    public message: string,
    public details?: any,
    public originalError?: Error | null
  ) {
    super(message);
    this.name = 'BridgeError';
  }
}

/**
 * BridgeAPI 接口，定义了与 SillyTavern 宿主环境通信的契约。
 */
export interface IBridgeAPI {
  /**
   * 高效地异步获取所有 Worldbook 的元数据列表，用于列表页展示。
   * @returns {Promise<WorldbookListItem[]>}
   */
  getWorldbookList(): Promise<WorldbookListItem[]>;
}

/**
 * BridgeAPI 的具体实现。
 * 这是一个适配器，封装了对全局 SillyTavern 对象的直接调用。
 */
export class BridgeAPI implements IBridgeAPI {
  /**
   * {@inheritdoc IBridgeAPI.getWorldbookList}
   */
  async getWorldbookList(): Promise<WorldbookListItem[]> {
    const parentWin = (window as any).parent;

    const TavernHelper = parentWin.TavernHelper;
    if (!TavernHelper || typeof TavernHelper.getWorldbookNames !== 'function' || typeof TavernHelper.getWorldbook !== 'function') {
      throw new BridgeError(
        'API_NOT_FOUND',
        'Required Worldbook API functions not found on parentWin.TavernHelper.',
        null,
        null
      );
    }

    try {
      const bookNames: string[] = await TavernHelper.getWorldbookNames();
      
      if (!Array.isArray(bookNames)) {
        throw new BridgeError('INVALID_RESPONSE', 'getWorldbookNames did not return an array.', bookNames);
      }

      const worldbookPromises = bookNames.map(async (name: string) => {
        const entries: WorldbookEntry[] = await TavernHelper.getWorldbook(name);
        return {
          name: name,
          entryCount: Array.isArray(entries) ? entries.length : 0,
        };
      });

      return Promise.all(worldbookPromises);
    } catch (error) {
      if (error instanceof BridgeError) {
        throw error;
      }
      throw new BridgeError(
        'GET_LIST_FAILED',
        'Failed to get worldbook list due to an unexpected error.',
        null,
        error instanceof Error ? error : null
      );
    }
  }

  // 其他方法可以后续实现
  // ...
}