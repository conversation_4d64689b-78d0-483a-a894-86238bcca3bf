// 世界书管理器扩展 - 采用SillyTavern标准模式
import { BridgeAPI } from './api';

(() => {
    'use strict';

    console.log('[WorldInfoOptimizer] Script execution started.');

    // 扩展配置常量
    const CONFIG = {
        EXTENSION_ID: 'world_info_optimizer',
        BUTTON_ID: 'world_info_optimizer_button',
        BUTTON_TEXT: '世界书管理器',
        BUTTON_ICON: 'fa-book',
        MAX_RETRIES: 100,
        RETRY_INTERVAL: 150
    };

    // 主应用类
    class WorldInfoOptimizerExtension {
        parentDoc: Document | null;
        parentWin: any;
        $: any;
        TavernHelper: any;
        bridge: BridgeAPI | null;

        constructor() {
            this.parentDoc = null;
            this.parentWin = null;
            this.$ = null;
            this.TavernHelper = null;
            this.bridge = null;
        }

        // 初始化入口
        async init() {
            console.log('[WorldInfoOptimizer] Initializing...');
            
            // 确保在父窗口环境中运行
            this.parentDoc = (window as any).parent.document;
            this.parentWin = (window as any).parent;
            this.$ = this.parentWin.jQuery;
            this.TavernHelper = this.parentWin.TavernHelper;

            if (!this.$ || !this.TavernHelper) {
                console.error('[WorldInfoOptimizer] Required APIs not available');
                return;
            }

            console.log('[WorldInfoOptimizer] APIs ready, creating UI...');
            
            // 初始化 BridgeAPI
            this.bridge = new BridgeAPI();

            this.createMenuButton();
        }

        // 创建扩展菜单按钮
        createMenuButton() {
            const extensionsMenu = this.parentDoc!.getElementById('extensionsMenu');
            if (!extensionsMenu) {
                console.error('[WorldInfoOptimizer] Extensions menu not found');
                return;
            }

            // 检查是否已存在
            if (this.parentDoc!.getElementById(CONFIG.BUTTON_ID)) {
                console.log('[WorldInfoOptimizer] Button already exists');
                return;
            }

            // 创建按钮HTML
            const buttonHtml = `
                <div id="${CONFIG.BUTTON_ID}" class="list-group-item flex-container flexGap5 interactable tavern-helper-shortcut-item" tabindex="0">
                    <div class="flex-container flexGap5">
                        <i class="fa-solid ${CONFIG.BUTTON_ICON}"></i>
                        <span>${CONFIG.BUTTON_TEXT}</span>
                    </div>
                </div>
            `;

            // 使用jQuery添加按钮和事件
            const $button = this.$(buttonHtml);
            this.$(extensionsMenu).append($button);

            // 绑定点击事件
            $button.on('click', () => {
                console.log('[WorldInfoOptimizer] Button clicked');
                this.showWorldInfoManager();
            });

            // 绑定键盘事件
            $button.on('keydown', (e: JQuery.KeyboardEventBase) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.showWorldInfoManager();
                }
            });

            console.log('[WorldInfoOptimizer] Button created successfully');
        }

        // 显示世界书管理器界面
        async showWorldInfoManager() {
            if (!this.bridge) {
                console.error('[WorldInfoOptimizer] BridgeAPI not initialized');
                return;
            }

            console.log('[WorldInfoOptimizer] Fetching worldbook list...');
            try {
                const bookList = await this.bridge.getWorldbookList();
                const message = `成功获取 ${bookList.length} 本世界书:\n\n` +
                    bookList.map(book => `${book.name} (${book.entryCount} 条目)`).join('\n');
                
                console.log('[WorldInfoOptimizer] Worldbook list:', bookList);
                this.parentWin.toastr?.success?.(message, CONFIG.BUTTON_TEXT, { timeOut: 10000 }) || 
                alert(message);

            } catch (error) {
                console.error('[WorldInfoOptimizer] Failed to fetch worldbook list:', error);
                const errorMessage = error instanceof Error ? error.message : '未知错误';
                this.parentWin.toastr?.error?.(`获取世界书列表失败: ${errorMessage}`, '错误') ||
                alert(`获取世界书列表失败: ${errorMessage}`);
            }
        }

        // 清理资源
        destroy() {
            const $button = this.$(`#${CONFIG.BUTTON_ID}`, this.parentDoc);
            if ($button.length > 0) {
                $button.remove();
                console.log('[WorldInfoOptimizer] Button removed');
            }
        }
    }

    // 初始化管理器
    const manager = new WorldInfoOptimizerExtension();

    // 等待SillyTavern环境就绪
    function waitForSillyTavern() {
        let retries = 0;
        
        const checkEnvironment = () => {
            const parentDoc = (window as any).parent.document;
            const parentWin = (window as any).parent;

            const checks = {
                domReady: parentDoc.getElementById('extensionsMenu') !== null,
                jQueryReady: typeof parentWin.jQuery === 'function',
                tavernHelperReady: typeof parentWin.TavernHelper === 'object' && parentWin.TavernHelper !== null,
                getWorldbookNamesReady: typeof parentWin.TavernHelper?.getWorldbookNames === 'function',
                getWorldbookReady: typeof parentWin.TavernHelper?.getWorldbook === 'function',
            };

            const allReady = Object.values(checks).every(Boolean);

            if (allReady) {
                console.log('[WorldInfoOptimizer] Environment ready, starting initialization...');
                try {
                    manager.init();
                } catch (error) {
                    console.error('[WorldInfoOptimizer] Initialization failed:', error);
                }
            } else {
                retries++;
                if (retries < CONFIG.MAX_RETRIES) {
                    setTimeout(checkEnvironment, CONFIG.RETRY_INTERVAL);
                } else {
                    const missingItems = Object.entries(checks)
                        .filter(([, value]) => !value)
                        .map(([key]) => key)
                        .join(', ');

                    const errorMessage = `[WorldInfoOptimizer] Initialization timeout. Missing dependencies: ${missingItems}. Please ensure you are running a compatible version of SillyTavern.`;
                    console.error(errorMessage);
                    parentWin.toastr?.error(errorMessage, 'World Info Optimizer Error', { timeOut: 15000 })
                        || alert(errorMessage);
                }
            }
        };

        // 立即开始检查
        checkEnvironment();
    }

    // 暴露全局访问
    (window as any).WorldInfoOptimizerExtension = manager;
    (window as any).initWorldInfoOptimizer = () => manager.init();

    // 启动等待流程
    waitForSillyTavern();
})();