{"scripts": {"build:dev": "webpack --mode development", "build": "webpack --mode production", "watch": "webpack --mode development --watch --progress", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,scss,html}\"", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix"}, "browserslist": ["defaults and partially supports es6-module"], "devDependencies": {"@eslint/js": "^9.34.0", "@tailwindcss/postcss": "^4.1.13", "@types/jquery": "^3.5.33", "@types/jqueryui": "^1.12.24", "@types/lodash": "^4.17.20", "@types/toastr": "^2.1.43", "@types/webpack": "^5.28.5", "@typescript-eslint/parser": "^8.42.0", "autoprefixer": "^10.4.21", "css-loader": "^7.1.2", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import-x": "^4.16.1", "eslint-webpack-plugin": "^5.0.2", "html-inline-css-loader": "^0.2.1", "html-inline-css-webpack-plugin": "^1.11.2", "html-inline-script-webpack-plugin": "^3.2.1", "html-loader": "^5.1.0", "html-webpack-inline-svg-plugin": "^2.3.0", "html-webpack-plugin": "^5.6.4", "mini-css-extract-plugin": "^2.9.4", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "postcss-loader": "^8.2.0", "postcss-minify": "^1.2.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "sass": "^1.92.0", "sass-loader": "^16.0.5", "socket.io": "^4.8.1", "tailwindcss": "^4.1.13", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.4", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "tslib": "^2.8.1", "type-fest": "^4.41.0", "typescript": "6.0.0-dev.20250807", "vue-loader": "^17.4.2", "vue-style-loader": "^4.1.3", "webpack": "^5.101.3", "webpack-cli": "^6.0.1"}, "dependencies": {"@vueuse/core": "^13.9.0", "dedent": "^1.7.0", "jquery": "^3.7.1", "jqueryui": "^1.11.1", "lodash": "^4.17.21", "pinia": "^3.0.3", "toastr": "^2.1.4", "vue": "^3.5.21", "vue-router": "^4.5.1", "yaml": "^2.8.1", "zod": "^4.1.5"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "@tailwindcss/oxide", "unrs-resolver"]}}