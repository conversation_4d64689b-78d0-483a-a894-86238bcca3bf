# 技术规格书 (Tech Spec) - Worldbook 管理器

**版本**: 2.1
**状态**: 架构修正与强化

---

## 1. 概述

本文档定义了“Worldbook 管理器”项目的技术架构、选型和开发规范。旨在为工程团队提供一份清晰的行动蓝图，确保项目具备高性能、高可维护性和高扩展性。

本文档的核心思想大量借鉴了项目前身 (`vue-migration-plan.md`) 中经过深入论证的顶级工程实践，并通过**v2.1**版本的实战调试经验进行了关键修正。

---

## 相关文档

* **[产品需求文档 (PRD)](./PRD.md)**: 本技术方案所服务的业务需求。
* **[项目路线图 (Roadmap)](./Roadmap.md)**: 本技术方案的实施路径。
* **[UX 设计与交互规范 (UX Spec)](./UX-Spec.md)**: UI构建策略的具体设计指导。
* **[API 设计文档 (BridgeAPI)](./BridgeAPI.md)**: 核心架构原则 `BridgeAPI` 的详细契约（已升级至v2.1）。

---

## 2. 最终技术栈

| 类别              | 技术/工具                    | 选用理由                                                                                                                     |
| :---------------- | :--------------------------- | :--------------------------------------------------------------------------------------------------------------------------- |
| **核心框架**      | Vue 3 + Webpack + TypeScript | 高度可配置的成熟打包工具，为将应用注入宿主环境提供了必要的细粒度控制。                                                       |
| **UI 框架**       | **Naive UI**                 | **开发效率最大化**。提供丰富、高质量且性能优越的组件，一流的TS支持，以及强大的主题定制能力。                                 |
| **UI 增强**       | jQuery UI Touch Punch        | 为 jQuery UI 组件提供移动端触摸事件支持，确保在手机等设备上能正常使用拖拽等交互。                                            |
| **状态管理**      | Pinia                        | Vue官方推荐的状态管理库，轻量、直观，与TypeScript和Vue DevTools完美集成。                                                    |
| **依赖加载**      | **JSDelivr CDN**             | 通过从 CDN (`testingcf.jsdelivr.net` 镜像) 加载 Vue 和 Naive UI 等第三方库，实现与宿主环境的依赖解耦，并大幅减小扩展包体积。 |
| **单元/组件测试** | Jest + Vue Testing Library   | 与 Webpack 生态系统良好集成的行业标准测试框架。                                                                              |
| **E2E 测试**      | Playwright                   | 功能强大且可靠的端到端测试框架，用于验证关键用户流程。                                                                       |
| **代码规范**      | ESLint + Prettier            | 保证代码风格统一，在编码阶段预防潜在错误。                                                                                   |

---

## 3. UI 入口与 DOM 注入策略

**此为整套技术架构的基石，是保证应用在 SillyTavern 环境中正确激活的关键。**

我们采用了一种基于**“好品味 (Good Taste)”**原则的 “Linus-style” 架构，它通过**IIFE封装**、**父窗口桥接**和**智能轮询**三大核心策略，确保了UI入口的极致健壮、高效与纯净。

### 3.1 核心架构决策

#### 3.1.1 IIFE 封装模式 - 消除全局污染
所有逻辑被封装在一个立即执行函数表达式（IIFE）中，实现了零全局变量泄露，确保脚本不会对 SillyTavern 的宿主环境造成任何污染。
```javascript
(function() {
    'use strict';
    // 所有逻辑封装在单一作用域
    // ...
})();
```

#### 3.1.2 父窗口桥接 - **TavernHelper 作为唯一入口**
为了最高效地与 SillyTavern 的 DOM 和 API 交互，我们直接桥接至父窗口。**经过实战验证，`parentWin.TavernHelper` 对象是与 SillyTavern 后端功能交互的唯一、正确的入口点。** 所有其他方式（如直接访问 `parentWin` 上的全局函数）都已被证实是不可靠的。
```javascript
this.parentDoc = window.parent.document;
this.parentWin = window.parent;
this.$ = this.parentWin.jQuery;
this.TavernHelper = this.parentWin.TavernHelper; // 关键API对象
```

#### 3.1.3 智能轮询 - **验证 TavernHelper API 就绪**
我们实现了一个健壮的环境检查机制。**此机制的核心是验证 `TavernHelper` 对象及其所需方法的可用性。** 只有在确认 API 完全就绪后，才会执行任何 DOM 操作或应用初始化。
```javascript
const checkEnvironment = () => {
    const parentWin = window.parent;
    const checks = {
        domReady: parentWin.document.getElementById('extensionsMenu') !== null,
        jQueryReady: typeof parentWin.jQuery === 'function',
        tavernHelperReady: typeof parentWin.TavernHelper === 'object' && parentWin.TavernHelper !== null,
        worldbookApiReady: typeof parentWin.TavernHelper?.getWorldbookNames === 'function',
    };
    
    if (Object.values(checks).every(Boolean)) {
        this.init(); // 立即初始化
    } else if (retries < CONFIG.MAX_RETRIES) {
        setTimeout(checkEnvironment, CONFIG.RETRY_INTERVAL);
    }
};
```
此机制拥有 **15秒** 的安全窗口（100次重试，150ms间隔），足以应对绝大多数加载场景，并能精确报告缺失的依赖项。

### 3.2 UI 激活入口实现

#### 3.2.1 按钮创建与事件委托
使用父窗口的 jQuery 实例，以确保与 SillyTavern 的 DOM 操作方式完全一致，从而避免任何兼容性问题。按钮创建后，通过事件委托绑定 `click` 和 `keydown` 事件，以支持键盘无障碍访问。
```javascript
const buttonHtml = `...`; // 符合SillyTavern UI规范的HTML
const $button = this.$(buttonHtml);

this.$('#extensionsMenu').append($button);

$button.on('click', () => this.showWorldInfoManager());
$button.on('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.showWorldInfoManager();
    }
});
```

#### 3.2.2 防重复注入
在注入按钮前，会检查 DOM 中是否已存在相同 ID 的元素，确保即使用户手动重复加载脚本，也不会造成 UI 元素的重复创建。
```javascript
if (this.parentDoc.getElementById(CONFIG.BUTTON_ID)) {
    return; // 如果按钮已存在，则中止
}
```

### 3.3 Vue 应用集成
在 `this.init()` 成功执行后，传统的 Vue 应用挂载流程 (`app.mount('#app')`) 将被触发。智能轮询机制确保了 Vue 应用只会在一个完全准备就绪的 DOM 环境中进行初始化。

---

## 4. 应用架构设计

我们将采用经过验证的、层次分明的应用架构，其核心是**接口先行**与**关注点分离**。

### 4.1 项目文件结构

为保持清晰和模块化，所有项目的源代码都将位于 `src/world_info_optimizer` 目录下。最终的构建产物也将从该目录生成。

```
src/
└── world_info_optimizer/
    ├── api/          # BridgeAPI 适配器层
    ├── assets/       # 静态资源
    ├── components/   # 可复用的Vue组件
    ├── store/        # Pinia状态管理
    ├── views/        # 页面级视图组件
    ├── App.vue       # 应用根组件
    └── main.ts       # 应用入口
```

### 4.2 核心架构原则: 防腐层 (`BridgeAPI`) - **基于 TavernHelper 的适配器**

**此为本架构的基石。**

所有与宿主环境 (SillyTavern) 的通信，都**必须**通过一个精确定义的 `BridgeAPI` 接口。这个接口是我们的应用与外部世界之间的唯一契约。

* **目的**: 作为应用的**防腐层 (Anti-Corruption Layer)**，它将我们的 Vue 应用与外部环境的实现细节和潜在变化完全隔离。
* **实现方式 - 适配器模式**: `BridgeAPI` 通过**适配器模式 (Adapter Pattern)** 实现。**它唯一的目标是封装对 `parentWin.TavernHelper` 对象的方法调用。** 任何对 `TavernHelper` 之外的全局对象的访问都应被视为技术债务。
* **职责**:
  * **封装**: 将所有对 `TavernHelper.getWorldbookNames()`, `TavernHelper.getWorldbook()` 等方法的直接调用封装在适配器内部。
  * **响应化**: 监听 `TavernHelper` 可能提供的事件，并将其转化为 Pinia Store 的更新操作，从而驱动 UI 自动刷新。
  * **解耦**: 确保 Vue 组件和 Pinia Store 只依赖于我们定义的 `BridgeAPI` 接口，而完全不知道 `TavernHelper` 对象的存在。
* **错误处理**: 通过统一的 `BridgeError` 类封装所有可能的错误场景，提供标准化的错误代码和用户友好的错误信息。
* **性能监控**: 内置性能指标收集功能，可实时监控API调用耗时和成功率。

> 关于 `BridgeAPI` 的完整方法、数据结构和事件模型的详细定义，请参阅 **[`BridgeAPI 设计文档`](./BridgeAPI.md)**（已升级至v2.1，包含完整的错误处理和性能监控实现）。

### 4.3 组件化拆分策略

遵循“原子设计”思想，并根据“列表 -> 详情”的新流程，将UI拆分为以下层次：

* **布局组件 (Layouts)**: 负责搭建应用的宏观结构，如 `WioPanel.vue` (整体面板), `WioToolbar.vue` (工具栏)。
* **视图组件 (Views)**: 对应各个主要功能页面，如 `WorldbookListView.vue` (列表页), `WorldbookDetailView.vue` (详情页)。
* **业务/原子组件 (Components)**: 可复用的最小功能单元，如 `WorldbookListItem.vue` (列表项), `EntryItem.vue` (详情页中的条目行)。

### 4.4 状态管理 (Pinia): 按领域拆分

为保证长期可维护性和扩展性，我们将从一开始就采用**按领域拆分**的 Pinia Store 模式。

* **`useWorldbookStore`**: 负责管理所有与 Worldbook 及其条目相关的核心数据和业务逻辑（如 `allWorldbooks`, `currentWorldbook`, `fetchWorldbooks`, `deleteEntry` 等）。
* **`useUIStateStore`**: 负责管理纯粹的UI状态和交互逻辑（如 `activeTab`, `isLoading`, `isMultiSelectMode`, `setLoadingState` 等）。

这种模式确保了**高内聚、低耦合**，使得代码更容易理解、测试和扩展。

---

## 5. 质量保证策略

* **开发阶段日志**: 在开发和调试阶段，应大幅增加控制台的日志输出 (`console.log`, `console.warn`)。清晰的日志是快速定位 `BridgeAPI` 通信问题、错误处理和性能监控的关键。在构建生产版本时，可以通过 Webpack 配置移除这些日志。
* **单元测试**: 所有核心业务逻辑（尤其是在 Pinia `actions` 中）都必须有单元测试覆盖。特别需要覆盖 `BridgeError` 类的所有错误场景和性能监控相关的逻辑。
* **组件测试**: 复杂的业务组件应编写组件测试，验证其不同 props 下的渲染和交互行为。
* **E2E 测试**: 针对核心的用户流程（如：新建 Worldbook -> 查看详情 -> 新建条目 -> 编辑条目 -> 删除）编写 E2E 测试，作为发布的最终门禁。
* **静态代码分析**: 强制执行 ESLint 和 Prettier 规则，不符合规范的代码不允许提交。

---

## 6. 风险分析与缓解

| 风险类别     | 具体风险描述                       | 缓解策略                                                                                               |
| :----------- | :--------------------------------- | :----------------------------------------------------------------------------------------------------- |
| **技术风险** | 样式冲突 (SillyTavern全局样式污染) | 1. **强制使用 `<style scoped>`**。<br>2. 采用BEM命名法辅助调试。<br>3. 在根组件中引入轻量级CSS Reset。 |
| **技术风险** | 大数据量下的性能瓶颈               | 针对长列表（Worldbook 列表、条目列表），在架构上预留**虚拟滚动**的实现接口。                           |
| **集成风险** | 宿主环境API变更 (`TavernHelper`)   | 所有对宿主环境的调用都必须通过一个统一的 `BridgeAPI` 模块。该模块作为**防腐层**，隔离外部变化。        |