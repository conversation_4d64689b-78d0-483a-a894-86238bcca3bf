# API 设计文档: BridgeAPI

**版本**: 2.1
**状态**: 已实现

---

## 1. 概述 (Overview)

**相关文档**: 本API的设计原则源于 **[`技术规格书 (Tech Spec)`](./TechSpec.md#42-核心架构原则-防腐层-bridgeapi)** 中的防腐层架构决策。

`BridgeAPI` 是 Worldbook 管理器应用与 SillyTavern 宿主环境之间通信的唯一接口，是应用架构的**防腐层 (Anti-Corruption Layer)**。

本文档详细定义了 `BridgeAPI` 的契约，包括其方法、数据结构和事件模型。

该接口通过**适配器模式 (Adapter Pattern)** 实现，**专门封装**对 `window.parent.TavernHelper` 对象的调用，为上层应用（Pinia Stores, Vue Components）提供了一个稳定、解耦且易于测试的依赖项。

### 1.1 上下文：API 的初始化

`BridgeAPI` 的实例化和使用，是整个 Vue 应用生命周期的一部分。而 Vue 应用的启动，则依赖于一个前置的、在 **[`技术规格书 (Tech Spec)`](./TechSpec.md#3-ui-入口与-dom-注入策略)** 中定义的健壮的 **UI入口** 机制。该机制通过智能轮询确保 SillyTavern 环境完全就绪后，才会挂载 Vue 应用，从而保证了 `BridgeAPI` 能在一个稳定、可预期的环境中被创建和使用。

## 2. 核心原则 (Core Principles)

- **解耦 (Decoupling)**: 应用的业务逻辑 **必须** 只依赖于 `BridgeAPI` 接口，**绝不** 直接访问全局的 `SillyTavern` 对象。
- **异步 (Asynchronous)**: 所有涉及文件 I/O 或与宿主环境有潜在耗时通信的方法，都 **必须** 返回 `Promise`。
- **事件驱动 (Event-Driven)**: 应用通过订阅 `BridgeAPI` 提供的事件来响应外部数据的变化，从而实现 UI 的响应式更新。
- **错误处理 (Error Handling)**: 所有方法都 **必须** 使用统一的错误处理机制，通过 `BridgeError` 类提供详细的错误信息。
- **性能监控 (Performance Monitoring)**: 关键操作 **必须** 包含性能监控和日志记录，便于调试和优化。

---

## 3. API 方法详解 (API Methods)

**注意**: 本节定义了 `BridgeAPI` 的完整形态。当前仅实现了标记为 **[已实现]** 的方法。

| 方法签名                                       | 返回值                         | 描述                                                                                                                                 |
| :--------------------------------------------- | :----------------------------- | :----------------------------------------------------------------------------------------------------------------------------------- |
| `loadWorldbook(name: string)`                  | `Promise<Worldbook \| null>`   | **[规划中]** 异步加载指定名称的 Worldbook。如果文件不存在或加载失败，应返回 `null`。                                                   |
| `saveWorldbook(name: string, data: Worldbook)` | `Promise<void>`                | **[规划中]** 异步将 `Worldbook` 对象保存到指定名称的文件中。如果文件已存在，则覆盖。                                                     |
| `getWorldbookList()`                           | `Promise<WorldbookListItem[]>` | **[已实现]** 高效地异步获取所有 Worldbook 的元数据列表（名称、条目数等），用于列表页展示。                                             |
| `getAllWorldbookNames()`                       | `Promise<string[]>`            | **[已废弃]** ~~异步获取所有可用的 Worldbook 名称列表。~~ **原因**: 性能低下，会导致 N+1 查询问题。请使用 `getWorldbookList()` 替代。 |
| `onWorldbookUpdated(callback: Function)`       | `() => void`                   | 注册一个回调函数，当宿主环境中的 Worldbook 数据更新时触发。返回一个用于取消订阅的函数。                                              |
| `getPerformanceMetrics()`                      | `Promise<PerformanceMetrics>`  | 获取性能监控数据，包括方法调用次数、平均执行时间等。                                                                                 |
| `generate(options: GenerateOptions)`           | `Promise<void>`                | 请求酒馆进行一次文本生成。                                                                                                           |
| `generateRaw(options: GenerateRawOptions)`     | `Promise<void>`                | 请求酒馆进行一次原始文本生成，通常用于特定格式或需要绕过常规处理流程的场景。                                                         |
| `stopGenerationById(id: string)`               | `Promise<void>`                | 停止一个由 `generate` 或 `generateRaw` 发起的、具有特定 `generation_id` 的生成任务。                                                 |
| `stopAllGeneration()`                          | `Promise<void>`                | 停止所有通过本API发起的生成任务（不影响酒馆自身的生成请求）。                                                                        |

---

## 4. 数据结构 (Data Structures)

所有数据结构均源自 `@types/iframe/exported.sillytavern.d.ts`，此处为方便参考而列出。

### 4.1 `WorldbookListItem`

为 `getWorldbookList()` 方法返回的轻量级对象，专为列表页性能优化设计。

| 字段名       | 类型     | 描述                          |
| :----------- | :------- | :---------------------------- |
| `name`       | `string` | Worldbook 的名称。            |
| `entryCount` | `number` | 该 Worldbook 包含的条目总数。 |

### 4.2 `Worldbook`

对应于 `SillyTavern.v2WorldInfoBook` 类型。

| 字段名    | 类型               | 描述                               |
| :-------- | :----------------- | :--------------------------------- |
| `name`    | `string`           | Worldbook 的名称。                 |
| `entries` | `WorldbookEntry[]` | 包含在该书中的所有条目对象的数组。 |

### 4.3 `WorldbookEntry`

对应于 `SillyTavern.v2DataWorldInfoEntry` 类型。

| 字段名            | 类型       | 描述                                     |
| :---------------- | :--------- | :--------------------------------------- |
| `keys`            | `string[]` | 触发此条目的主关键词数组。               |
| `content`         | `string`   | 条目的主要内容。                         |
| `enabled`         | `boolean`  | 此条目当前是否启用。                     |
| `comment`         | `string`   | 对条目的注释或描述。                     |
| `secondary_keys`  | `string[]` | 触发此条目的次要关键词数组。             |
| `constant`        | `boolean`  | 内容是否为固定不变。                     |
| `selective`       | `boolean`  | 是否选择性地包含。                       |
| `insertion_order` | `number`   | 注入顺序的优先级。                       |
| `position`        | `string`   | 注入位置 ("before_char", "after_char")。 |
| `id`              | `number`   | 条目的唯一ID。                           |
| `extensions`      | `object`   | 包含其他扩展属性的对象。                 |

---

## 5. 事件模型 (Event Model)

### 5.1 `onWorldbookUpdated`

- **触发时机**: 当用户在 SillyTavern 的其他地方（如原生UI）修改并保存了任何 Worldbook 时。
- **对应事件**: `tavern_events.WORLDINFO_UPDATED`
- **回调函数签名**: `(name: string, data: { entries: WorldbookEntry[] }) => void`
  - `name`: 被更新的 Worldbook 的名称。
  - `data`: 包含该 Worldbook 最新条目列表的对象。

---

## 6. 错误处理 (Error Handling)

### 6.1 `BridgeError` 类

所有 API 方法在遇到错误时都会抛出 `BridgeError` 实例，包含以下属性：

| 属性            | 类型            | 描述                     |
| :-------------- | :-------------- | :----------------------- |
| `code`          | `string`        | 错误代码，用于程序化处理 |
| `message`       | `string`        | 人类可读的错误描述       |
| `details`       | `any`           | 可选的详细错误信息       |
| `originalError` | `Error \| null` | 原始的错误对象（如果有） |

### 6.2 常见错误代码

| 错误代码            | 描述                        |
| :------------------ | :-------------------------- |
| `FILE_NOT_FOUND`    | 请求的 Worldbook 文件不存在 |
| `PERMISSION_DENIED` | 没有足够的权限执行操作      |
| `INVALID_DATA`      | 数据格式无效或损坏          |
| `NETWORK_ERROR`     | 网络通信失败                |
| `TIMEOUT`           | 操作超时                    |
| `UNKNOWN_ERROR`     | 未知错误                    |

## 7. 性能监控 (Performance Monitoring)

### 7.1 `PerformanceMetrics` 接口

| 字段名             | 类型            | 描述                              |
| :----------------- | :-------------- | :-------------------------------- |
| `loadWorldbook`    | `MethodMetrics` | `loadWorldbook` 方法的性能数据    |
| `saveWorldbook`    | `MethodMetrics` | `saveWorldbook` 方法的性能数据    |
| `getWorldbookList` | `MethodMetrics` | `getWorldbookList` 方法的性能数据 |

### 7.2 `MethodMetrics` 接口

| 字段名        | 类型                  | 描述                   |
| :------------ | :-------------------- | :--------------------- |
| `callCount`   | `number`              | 方法调用次数           |
| `totalTime`   | `number`              | 总执行时间（毫秒）     |
| `averageTime` | `number`              | 平均执行时间（毫秒）   |
| `lastError`   | `BridgeError \| null` | 最后一次错误（如果有） |

## 8. 使用示例 (Usage Example)

以下伪代码展示了如何在 Pinia store 中使用 `BridgeAPI`。

```typescript
import { defineStore } from 'pinia';
import { IBridgeAPI } from './BridgeAPI'; // 假设类型从接口文件导入

// bridge 实例通过依赖注入传入
export const useWorldbookStore = defineStore('worldbook', (bridge: IBridgeAPI) => {
  const state = ref({
    currentBook: null as Worldbook | null,
    isLoading: false,
  });

  async function fetchBook(name: string) {
    state.value.isLoading = true;
    try {
      state.value.currentBook = await bridge.loadWorldbook(name);
    } catch (error) {
      if (error instanceof BridgeError) {
        console.error(`Failed to load book ${name}:`, error.code, error.message);
        // 根据错误代码进行特定处理
        if (error.code === 'FILE_NOT_FOUND') {
          // 处理文件不存在的情况
        }
      }
      throw error; // 重新抛出以便上层处理
    } finally {
      state.value.isLoading = false;
    }
  }
  
  // 在 store 初始化时设置监听
  const unsubscribe = bridge.onWorldbookUpdated((name, data) => {
    if (state.value.currentBook?.name === name) {
      console.log(`Current book ${name} was updated externally.`);
      state.value.currentBook.entries = data.entries;
    }
  });

  return { state, fetchBook, unsubscribe };
});
