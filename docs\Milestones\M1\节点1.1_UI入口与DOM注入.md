*   **[X] 节点 1.11: 环境检查机制 (Environment Check)**
    *   **负责人 (Owner)**: **Architect / Developer**
    *   **关联用户故事**: "作为用户，我希望应用能可靠地启动，并在扩展菜单中显示入口"
    *   **任务**: 实现一个健壮的轮询检查机制，在UI注入前，**必须同时验证**以下条件：1. 关键DOM元素（如 `#extensionsMenu`）已加载；2. 关键的JavaScript API对象（`window.parent.jQuery` 和 `window.parent.TavernHelper`）及其所需方法（如 `TavernHelper.getWorldbookNames`）已准备就绪。必须处理超时和依赖缺失的异常情况，并向用户提供清晰的错误报告。
    *   **验收标准**: 1. 脚本能正确等待SillyTavern DOM和**关键API**加载完成。 2. 在依赖未就绪时，脚本会持续轮询，直到超时。 3. 如果超时，脚本必须在控制台打印出具体**缺失的依赖项**（例如：`TavernHelper` 或 `getWorldbookNamesReady`），并向用户显示一个明确的错误提示。 4. 在所有依赖都就绪后，控制台会输出清晰的就绪状态日志，并开始执行初始化。
    *   **参考实现**: 参考 `src/Extension-PromptInspector/index.js` 中的 `addLaunchButton()` 函数的直接DOM注入模式。
*   **[X] 节点 1.12: UI 激活按钮创建 (Activation Button)**
    *   **负责人 (Owner)**: **Architect / Developer**
    *   **关联用户故事**: "作为用户，我希望在扩展菜单中看到清晰的激活按钮"
    *   **任务**: 创建符合SillyTavern UI风格的激活按钮，包含正确的CSS类(`list-group-item`, `flex-container`, `flexGap5`, `interactable`)和图标。设置合适的提示文本和点击事件。
    *   **验收标准**: 按钮正确显示在SillyTavern的扩展菜单中，具有响应式的点击事件和视觉反馈。
*   **[X] 节点 1.13: API 调用与临时反馈 (API Call & Temporary Feedback)**
    *   **负责人 (Owner)**: **Architect / Developer**
    *   **关联用户故事**: "作为用户，我希望点击按钮后能看到应用的主界面" (部分实现)
    *   **任务**: 实现点击按钮后调用 `BridgeAPI` 的 `getWorldbookList` 方法，并使用 `toastr` 或 `alert` 作为临时反馈机制，显示获取到的数据摘要。
    *   **验收标准**: 点击按钮后，能够成功调用API，并弹出一个包含正确 Worldbook 数量和名称的通知。
*   **[X] 节点 1.14: 事件绑定与无障碍访问 (Event Binding & Accessibility)**
    *   **负责人 (Owner)**: **Architect / Developer**
    *   **关联用户故事**: "作为用户，我希望在扩展菜单中看到清晰的激活按钮"
    *   **任务**: 为激活按钮同时绑定 `click` 和 `keydown` (Enter/Space) 事件，确保鼠标和键盘用户都能正常激活功能，符合无障碍访问标准。
    *   **验收标准**: 通过鼠标点击或键盘回车/空格键都能触发 API 调用和临时反馈。