// src/world_info_optimizer/tavern-helper-types.ts

/**
 * TavernHelper 接口类型定义
 * 这些类型定义基于 SillyTavern 的 TavernHelper API
 */

export interface TavernRegexOptions {
  scope?: 'all' | 'global' | 'character';
}

export interface LorebookSettings {
  selected_global_lorebooks?: string[];
  [key: string]: any;
}

export interface CharacterLorebookData {
  primary?: string;
  additional?: string[];
}

export interface CharacterData {
  name: string;
  [key: string]: any;
}

export interface SillyTavernContext {
  characters: CharacterData[];
  characterId?: number | null;
  chatId?: number | null;
}

export interface Character {
  getRegexScripts(): RegexScript[];
}

export interface RegexScript {
  id?: string;
  scriptName?: string;
  findRegex: string;
  replaceString: string;
  disabled?: boolean;
}

export interface TavernHelper {
  // Lorebook APIs
  createLorebook(name: string): Promise<any>;
  deleteLorebook(name: string): Promise<any>;
  getLorebooks(): Promise<string[]>;
  getLorebookSettings(): Promise<LorebookSettings>;
  setLorebookSettings(settings: LorebookSettings): Promise<any>;

  // Lorebook Entry APIs
  getLorebookEntries(name: string): Promise<any[]>;
  setLorebookEntries(name: string, entries: any[]): Promise<any>;
  createLorebookEntries(name: string, entries: any[]): Promise<any[]>;
  deleteLorebookEntries(name: string, uids: string[]): Promise<any>;

  // Character-specific Lorebook APIs
  getCharLorebooks(charData?: CharacterData): Promise<CharacterLorebookData>;
  setCurrentCharLorebooks(lorebooks: string[]): Promise<any>;

  // Chat Lorebook APIs
  getChatLorebook(): Promise<string | null>;
  setChatLorebook(name: string | null): Promise<any>;
  getOrCreateChatLorebook(name: string): Promise<any>;

  // Regex APIs
  getTavernRegexes(options: TavernRegexOptions): Promise<any[]>;
  replaceTavernRegexes(regexes: any[], options: TavernRegexOptions): Promise<any>;

  // Character Data APIs
  getCharData(): Promise<CharacterData>;

  // Character constructor
  Character: new (data: any) => Character;

  // Built-in utilities
  builtin: {
    saveSettings(): Promise<any>;
  };
}

// Global window extensions
declare global {
  interface Window {
    TavernHelper: TavernHelper;
    SillyTavern: {
      getContext(): SillyTavernContext;
    };
    Swal: any;
    jQuery: JQueryStatic;
  }
}
