# API 集成策略 (API Integration Strategy)

**版本**: 1.0
**状态**: 生效中

---

## 1. 概述

本文档定义了本项目与 SillyTavern 宿主环境进行 API 集成时的核心原则、流程和策略。其目的是将在最近的调试过程中获得的宝贵经验制度化，以确保未来所有与外部环境的交互都是健壮、可靠且可维护的。

本策略是 **[技术规格书 (Tech Spec)](./TechSpec.md)** 和 **[BridgeAPI 设计文档](./BridgeAPI.md)** 的高阶指导原则。

---

## 2. 核心原则

### 2.1 单一入口原则 (Single Entry Point Principle)

**所有**与 SillyTavern 宿主环境的功能性 API 的交互，都**必须**通过 `window.parent.TavernHelper` 这一个对象来完成。

*   **Rationale**: 实战调试证明，`TavernHelper` 是 SillyTavern 官方设计的、用于扩展开发的稳定 API 接口。直接访问 `window.parent` 上的其他全局函数或对象已被证实是不可靠的，并且可能在未来的 SillyTavern 版本中被更改或移除，从而导致扩展失效。
*   **Action**: 严禁在 `BridgeAPI` 之外的任何地方直接访问 `window.parent` 上的任何属性（`jQuery` 和 `document` 除外）。`BridgeAPI` 是唯一允许与 `TavernHelper` 直接交互的模块。

### 2.2 防腐层原则 (Anti-Corruption Layer Principle)

我们的应用代码（Vue 组件、Pinia Stores 等）**绝不能**直接了解或依赖 `TavernHelper` 的任何实现细节。**`BridgeAPI` 是我们的应用与 `TavernHelper` 之间唯一且必要的防腐层。**

*   **Rationale**: 通过将所有外部交互封装在 `BridgeAPI` 中，我们将应用内部逻辑与外部环境的变化完全解耦。如果未来 `TavernHelper` 的 API 发生变化，我们只需要修改 `BridgeAPI` 这个适配器，而无需触及任何核心应用代码。
*   **Action**: 所有数据获取、状态变更和事件监听都必须通过 `BridgeAPI` 提供的接口来完成。

---

## 3. API 发现与验证流程

当需要集成一个新的 SillyTavern 功能时，必须遵循以下流程来发现和验证正确的 API 端点：

1.  **审查类型定义**: 首先，仔细审查项目中的 `@types/iframe/exported.tavernhelper.d.ts` 文件。这是了解 `TavernHelper` 提供了哪些可用方法和数据结构的最快途径。

2.  **寻找官方示例**: 其次，在 SillyTavern 的官方文档（如果存在）或其源代码库中搜索相关 API 的使用示例。官方示例是理解 API 预期行为的最佳参考。

3.  **参考现有实现**: 再次，参考本项目或其他已知的、功能稳定的第三方扩展是如何与该 API 进行交互的。这可以提供宝贵的实践经验，避免重复踩坑。

4.  **编写集成测试**: **在将新 API 集成到主应用之前，必须先编写一个独立的、小型的集成测试用例来验证其行为。** 这个测试应该：
    *   在类似 SillyTavern 的环境中运行（例如，一个简单的 HTML 文件）。
    *   直接调用 `TavernHelper` 上的新 API。
    *   断言其返回值、副作用是否符合预期。
    *   验证其在不同参数下的行为和错误处理逻辑。

这个流程确保了我们在集成任何新功能之前，都对其有充分的理解和信心，从而最大限度地减少未来出现意外行为的风险。

---

## 4. 版本兼容性策略

`TavernHelper` 的 API 可能会随着 SillyTavern 的版本迭代而发生变化。我们必须采取主动策略来保证扩展的前向兼容性。

*   **特性检测 (Feature Detection)**: 在 `BridgeAPI` 内部，针对所有非核心的 API 调用，都应进行特性检测。
    ```typescript
    // 示例: 检查一个可选的API是否存在
    if (typeof TavernHelper.someOptionalApi === 'function') {
        // 使用新API
    } else {
        // 执行优雅降级逻辑
    }
    ```

*   **优雅降级 (Graceful Degradation)**: 当一个期望的 API 不存在时，应用不应崩溃。`BridgeAPI` 应负责提供一个合理的默认行为或备用方案，并确保应用的其他部分能够正常运行。

*   **日志警告**: 当检测到某个 API 已被废弃或其行为与预期不符时，应在开发者控制台中打印清晰、详细的警告信息，以便于问题的快速发现和定位。

---

## 5. 统一错误处理模型

所有源自 `TavernHelper` 的同步或异步错误，都**必须**在 `BridgeAPI` 的适配器方法内部被捕获。

捕获到的原始错误随后必须被包装成一个标准的、自定义的 `BridgeError` 对象，并向上层抛出。

*   **Rationale**: 这确保了我们的应用上层（Stores 和 Components）只需要处理一种统一的、可预测的错误类型。它们无需关心底层的具体错误实现，只需根据 `BridgeError` 提供的 `code` 和 `message` 来执行相应的用户提示或状态更新即可。
*   **Action**: 每个与 `TavernHelper` 交互的方法都必须包含 `try...catch` 块，并正确实现错误转换逻辑。