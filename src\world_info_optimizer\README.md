# 世界书管理器扩展

## 功能描述

在SillyTavern的扩展菜单中添加"世界书管理器"按钮，提供快速访问世界书(World Info)管理功能的入口。

## 安装方法

1. 构建扩展：
   ```bash
   npm run build
   ```

2. 将生成的文件复制到SillyTavern的扩展目录：
   - 源文件：`dist/world_info_optimizer/index.js`
   - 目标：`SillyTavern/scripts/extensions/third-party/world_info_optimizer/index.js`

## 使用说明

1. 启动SillyTavern
2. 点击右上角的扩展菜单按钮
3. 在扩展菜单中找到"世界书管理器"按钮
4. 点击按钮即可打开世界书管理界面

## 技术细节

- 扩展ID：`world_info_optimizer`
- 按钮ID：`world_info_optimizer_button`
- 图标类：`fa-solid fa-book`
- 自定义事件：`worldInfoOptimizer:open`

## 开发扩展

要监听世界书管理器的打开事件，可以在其他脚本中添加：

```javascript
document.addEventListener('worldInfoOptimizer:open', (event) => {
    console.log('世界书管理器已打开', event.detail);
    // 这里可以添加打开管理界面的逻辑
});
```