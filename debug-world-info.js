// 世界书管理器调试工具
// 在SillyTavern控制台中运行这些命令

console.log('=== 世界书管理器调试工具 ===');

// 1. 检查扩展菜单是否存在
console.log('1. 扩展菜单状态检查:');
console.log('   extensionsMenu存在:', !!document.getElementById('extensionsMenu'));
console.log('   当前DOM加载状态:', document.readyState);

// 2. 检查所有可能的扩展菜单容器
console.log('2. 扩展菜单容器搜索:');
const possibleContainers = [
    '#extensionsMenu',
    '.extensions-menu',
    '[data-extensions-menu]',
    '.extension-list',
    '#extension-list'
];

possibleContainers.forEach(selector => {
    const element = document.querySelector(selector);
    console.log(`   ${selector}:`, !!element, element ? element.id : '未找到');
});

// 3. 查找所有包含扩展按钮的容器
console.log('3. 扩展按钮容器分析:');
const extensionButtons = document.querySelectorAll('[id*="wand_container"], .extension_container, .list-group-item');
console.log('   找到的扩展按钮数量:', extensionButtons.length);
extensionButtons.forEach((btn, i) => {
    console.log(`   ${i + 1}.`, btn.id || btn.className, btn.textContent.trim().substring(0, 20));
});

// 4. 动态监听扩展菜单创建
console.log('4. 设置扩展菜单监听器...');
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
                const extensionsMenu = node.querySelector('#extensionsMenu') || 
                                     (node.id === 'extensionsMenu' ? node : null);
                if (extensionsMenu) {
                    console.log('🎉 扩展菜单已创建！');
                    console.log('   菜单ID:', extensionsMenu.id);
                    console.log('   菜单内容:', extensionsMenu.innerHTML.substring(0, 100));
                    
                    // 现在可以安全地初始化世界书管理器
                    if (window.WorldInfoOptimizerExtension) {
                        window.WorldInfoOptimizerExtension.createMenuButton();
                    }
                }
            }
        });
    });
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

// 5. 手动触发初始化（备用方案）
window.initWorldInfoOptimizer = () => {
    console.log('手动初始化世界书管理器...');
    if (window.WorldInfoOptimizerExtension) {
        window.WorldInfoOptimizerExtension.createMenuButton();
    } else {
        console.error('WorldInfoOptimizerExtension 未定义');
    }
};

// 6. 检查全局变量
console.log('6. 全局变量检查:');
console.log('   WorldInfoOptimizerExtension:', !!window.WorldInfoOptimizerExtension);
console.log('   getContext:', !!window.getContext);

console.log('=== 调试工具加载完成 ===');
console.log('使用 initWorldInfoOptimizer() 手动初始化');