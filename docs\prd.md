# 产品需求文档 (PRD) - Worldbook 管理器 (新项目)

**版本**: 1.2
**状态**: 修订

---

## 相关文档

*   **[技术规格书 (Tech Spec)](./TechSpec.md)**: 定义了项目的技术架构、选型和开发规范。
*   **[项目路线图 (Roadmap)](./Roadmap.md)**: 规划了项目的开发里程碑和功能交付顺序。
*   **[UX 设计与交互规范 (UX Spec)](./UX-Spec.md)**: 描述了产品的用户体验、界面布局和交互流程。
*   **[API 设计文档 (BridgeAPI)](./BridgeAPI.md)**: 详细定义了应用与宿主环境通信的接口契约。

---

## 1. 项目愿景与目标

### 1.1 愿景
打造一款强大、直观且高效的SillyTavern **Worldbook** 管理工具，让用户能够轻松地组织、编辑和优化他们创作内容中的知识库，从而提升角色扮演的沉浸感和一致性。

### 1.2 核心目标
*   **效率提升**: 提供强大的批量操作和搜索功能，将用户从繁琐的手动编辑中解放出来。
*   **直观易用**: 设计一个清晰、现代化的用户界面，让所有技能水平的用户都能快速上手。
*   **健壮稳定**: 构建一个基于现代化技术栈的、可维护、可扩展的系统，为未来的功能迭代奠定坚实基础。

### 1.3 当前状态
**基石已奠定**：项目已完成 **节点 1.1 (UI 入口)** 的开发，实现了一个极其健壮的UI入口。此入口确保了扩展能在各种复杂的加载场景下稳定、可靠地注入到 SillyTavern 界面中，为后续所有功能的实现提供了坚实保障。

---

## 2. 核心用户故事

### 2.1 数据管理
*   **作为用户, 我希望应用能在我打开时加载所有必要数据**, 包括全局和角色的 **Worldbook**，以便我能获得一个完整的概览。
*   **作为用户, 我希望能手动刷新所有数据**, 以便与在扩展之外所做的任何更改保持同步。
*   **作为用户, 我希望在切换角色时应用能执行局部刷新**, 仅加载与角色相关的 **Worldbook** 数据，以提升性能。

### 2.2 界面与导航
*   **作为用户, 我希望有一个清晰的导航流程**: 首先看到一个 **Worldbook 列表**，点击其中一项后，再进入该 **Worldbook 的条目详情页面**。
*   **作为用户, 我希望有一个标签页界面来切换不同的 **Worldbook** 视图**：全局 Worldbooks、角色 Worldbooks、聊天 Worldbooks。
*   **作为用户, 我希望界面能清晰地指示数据加载状态**，避免我在等待时感到困惑。

### 2.3 Worldbook 管理 (列表页)
*   **作为用户, 我希望在列表页可以对 **Worldbook** 进行完整的创建、重命名和删除操作**。
*   **作为用户, 我希望可以在列表页一键启用或禁用全局 **Worldbook**。

### 2.4 Worldbook 条目管理 (详情页)
*   **作为用户, 我希望在详情页可以对 **Worldbook** 条目进行完整的创建、编辑和删除操作**。
*   **作为用户, 我希望可以方便地折叠或展开单个条目内容**, 以便在条目众多时保持视图整洁。
*   **作为用户, 我希望可以对多个条目进行批量操作**, 例如批量启用或禁用。

### 2.5 搜索与替换
*   **作为用户, 我希望有一个强大的搜索栏**，在列表页可以过滤 **Worldbook**，在详情页可以过滤条目。
*   **作为用户, 我希望可以指定搜索范围**（例如：书名、条目名、关键词、内容）。
*   **作为用户, 我希望可以在搜索结果中执行查找和替换操作**。

### 2.6 操作反馈
*   **作为用户, 我希望看到清晰的模态框用于确认和提示**。
*   **作为用户, 我希望在操作成功或进行中时，能收到非侵入式的即时通知**。

---

## 3. 功能需求清单

### 3.1 核心视图与状态
*   [ ] **加载状态**: 当应用正在加载数据时，必须显示明确的加载指示器。
*   [ ] **空状态**: 当没有数据或搜索结果为空时，必须显示友好的提示信息。
*   [ ] **错误状态**: 当数据加载失败时，必须显示错误信息和重试按钮。

### 3.2 Worldbook 列表页
*   [ ] **渲染**: 能够以列表（或卡片列表）的形式渲染所有 **Worldbook**。
*   [ ] **控件**:
    *   [ ] 提供新建 **Worldbook** 按钮。
    *   [ ] 每一项应提供重命名按钮。
    *   [ ] 每一项应提供删除按钮（需二次确认）。
    *   [ ] (仅全局视图) 每一项应提供启用/禁用的开关。
*   [ ] **信息展示**:
    *   [ ] 正确显示 **Worldbook** 名称和条目总数。
    *   [ ] 若 **Worldbook** 被角色使用，需显示使用标识。
*   [ ] **导航**: 点击列表中的任意一项，应导航至该 **Worldbook** 的详情页。

### 3.3 Worldbook 详情页
*   [ ] **导航**: 提供明确的返回“列表页”的入口。
*   [ ] **信息展示**: 清晰展示当前 **Worldbook** 的名称。
*   [ ] **条目渲染**:
    *   [ ] 能够以可折叠面板的形式渲染该 **Worldbook** 下的所有条目。
    *   [ ] 提供新建条目按钮。
*   [ ] **条目控件**:
    *   [ ] 每个条目都应提供折叠/展开功能。
    *   [ ] 每个条目都应提供编辑按钮，点击后应弹出条目编辑器。
    *   [ ] 每个条目都应提供删除按钮（需二次确认）。
    *   [ ] 每个条目都应提供启用/禁用的开关。
*   [ ] **条目信息展示**:
    *   [ ] 正确显示条目名称和关键词。
    *   [ ] 条目名称和关键词能高亮匹配的搜索文本。

### 3.4 交互功能
*   [ ] **标签页切换**: 支持在不同 **Worldbook** 视图（全局、角色、聊天）间切换。
*   [ ] **全局刷新**: 提供一个能重新加载所有数据的“刷新”按钮。
*   [ ] **角色刷新**: 在角色相关视图下，提供一个仅刷新当前角色数据的按钮。
*   [ ] **多选模式 (详情页)**:
    *   [ ] 提供进入/退出多选模式的开关。
    *   [ ] 在多选模式下，每个条目前应出现复选框。
    *   [ ] 提供基于多选内容的批量操作按钮（删除、启用、禁用）。
*   [ ] **批量搜索与替换 (详情页)**:
    *   [ ] 提供输入“搜索词”和“替换词”的文本框。
    *   [ ] 提供“全部替换”按钮，执行前需弹窗确认。

---

## 4. 非功能性需求

*   **性能**: 应用的响应速度和数据加载时间必须达到行业主流标准，确保流畅的用户体验。
*   **兼容性**: 扩展必须与最新版本的SillyTavern保持完全兼容。
*   **代码质量**: 所有代码必须遵循统一的编码规范，具备良好的文档和可测试性。