// src/world_info_optimizer/integration-test.ts
// 这个文件用于验证所有模块的集成是否正确

/**
 * 集成测试：验证所有模块导入导出是否正确
 */

// 测试主要模块导入
import { injectUI, initUI } from './ui';
import { initializeEventHandlers } from './events';
import {
  setupResponsiveResizeHandler,
  applyHighContrastMode,
  applyReducedMotionMode,
  applyDeviceOptimizations,
  getScreenSize,
  getDeviceType,
  isTouchDevice,
} from './ui/helpers';

// 测试状态管理
import {
  getState,
  updateState,
  subscribe,
  setActiveTab,
  setDataLoaded,
  setSearchQuery,
  setAllData,
  toggleMultiSelectMode,
  toggleItemSelection,
  clearSelection,
  updateSearchFilters,
} from './store';

// 测试核心功能
import {
  loadAllData,
  createLorebook,
  renameLorebook,
  deleteLorebook,
  createLorebookEntry,
  updateLorebookEntry,
  deleteLorebookEntry,
  setGlobalLorebookEnabled,
  createRegex,
  updateRegex,
  deleteRegex,
} from './core';

// 测试API封装
import { TavernAPI } from './api';

// 测试UI组件
import {
  renderGlobalLorebookView,
  renderCharacterLorebookView,
  renderChatLorebookView,
  renderRegexView,
} from './ui/views';

import { createLorebookElement, createEntryElement, createRegexItemElement } from './ui/elements';

import { showModal, showSuccessTick, showEntryEditorModal, showProgressToast, showRegexEditorModal } from './ui/modals';

// 测试类型定义
import type {
  AppState,
  TavernRegex,
  LorebookFile,
  LorebookEntry,
  ModalOptions,
  EntryEditorOptions,
  RegexEditorOptions,
  DeepReadonly,
  StateUpdater,
  EventHandler,
  AsyncResult,
  SearchFilterKey,
  TabId,
  DeviceType,
  ScreenSize,
  ResponsiveCallback,
} from './types';

import type { TavernHelper } from './tavern-helper-types';

// 测试常量
import {
  PANEL_ID,
  BUTTON_ID,
  SEARCH_INPUT_ID,
  REFRESH_BTN_ID,
  CREATE_LOREBOOK_BTN_ID,
  BUTTON_ICON_URL,
  BUTTON_TEXT_IN_MENU,
  INITIAL_ACTIVE_TAB,
} from './constants';

/**
 * 运行集成测试
 */
export const runIntegrationTest = (): boolean => {
  console.log('[WIO Integration Test] Starting integration test...');

  try {
    // 测试类型检查
    const testState: AppState = {
      regexes: { global: [], character: [] },
      lorebooks: { character: [] },
      chatLorebook: null,
      allLorebooks: [],
      lorebookEntries: new Map(),
      lorebookUsage: new Map(),
      activeTab: INITIAL_ACTIVE_TAB,
      isDataLoaded: false,
      searchFilters: {
        bookName: true,
        entryName: true,
        keywords: true,
        content: true,
      },
      searchQuery: '',
      multiSelectMode: false,
      selectedItems: new Set(),
    };

    // 测试函数存在性
    const functions = [
      injectUI,
      initUI,
      initializeEventHandlers,
      setupResponsiveResizeHandler,
      applyHighContrastMode,
      applyReducedMotionMode,
      applyDeviceOptimizations,
      getScreenSize,
      getDeviceType,
      isTouchDevice,
      getState,
      updateState,
      subscribe,
      setActiveTab,
      setDataLoaded,
      setSearchQuery,
      setAllData,
      toggleMultiSelectMode,
      toggleItemSelection,
      clearSelection,
      updateSearchFilters,
      loadAllData,
      createLorebook,
      renameLorebook,
      deleteLorebook,
      createLorebookEntry,
      updateLorebookEntry,
      deleteLorebookEntry,
      setGlobalLorebookEnabled,
      createRegex,
      updateRegex,
      deleteRegex,
      renderGlobalLorebookView,
      renderCharacterLorebookView,
      renderChatLorebookView,
      renderRegexView,
      createLorebookElement,
      createEntryElement,
      createRegexItemElement,
      showModal,
      showSuccessTick,
      showEntryEditorModal,
      showProgressToast,
      showRegexEditorModal,
    ];

    for (const func of functions) {
      if (typeof func !== 'function') {
        throw new Error(`Function ${func.name} is not properly exported`);
      }
    }

    // 测试对象存在性
    if (!TavernAPI || typeof TavernAPI !== 'object') {
      throw new Error('TavernAPI is not properly exported');
    }

    // 测试常量
    const constants = [
      PANEL_ID,
      BUTTON_ID,
      SEARCH_INPUT_ID,
      REFRESH_BTN_ID,
      CREATE_LOREBOOK_BTN_ID,
      BUTTON_ICON_URL,
      BUTTON_TEXT_IN_MENU,
      INITIAL_ACTIVE_TAB,
    ];

    for (const constant of constants) {
      if (typeof constant !== 'string') {
        throw new Error(`Constant ${constant} is not properly defined`);
      }
    }

    console.log('[WIO Integration Test] ✅ All modules integrated successfully!');
    return true;
  } catch (error) {
    console.error('[WIO Integration Test] ❌ Integration test failed:', error);
    return false;
  }
};

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
  runIntegrationTest();
}
