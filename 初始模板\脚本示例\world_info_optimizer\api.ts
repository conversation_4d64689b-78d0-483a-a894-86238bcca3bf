// src/world_info_optimizer/api.ts

import { TavernHelper } from './tavern-helper-types';
import { showModal } from './ui/modals';

/**
 * 错误处理装饰器或高阶函数，用于封装API调用。
 * @param fn 要封装的异步函数
 * @param context 错误日志的上下文
 */
const errorCatched = <T extends (...args: any[]) => Promise<any>>(fn: T, context: string = 'TavernAPI') => {
  return async (...args: Parameters<T>): Promise<ReturnType<T> | null> => {
    try {
      return await fn(...args);
    } catch (error) {
      // 用户取消操作（例如在模态框中点击取消）通常会抛出null或undefined
      if (error) {
        console.error(`[${context}] Error:`, error);
        // 可以在这里决定是否显示一个全局的错误提示
        await showModal({
          type: 'alert',
          title: 'API调用异常',
          text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,
        });
      }
      return null;
    }
  };
};

/**
 * 获取全局TavernHelper实例。
 * @returns {TavernHelper}
 * @throws 如果TavernHelper未定义，则抛出错误。
 */
const getTavernHelper = (): TavernHelper => {
  const parentWin = window.parent as any;
  if (!parentWin.TavernHelper) {
    throw new Error('TavernHelper is not available on the parent window.');
  }
  return parentWin.TavernHelper;
};

const getSillyTavernContext = () => {
  const parentWin = window.parent as any;
  if (!parentWin.SillyTavern || typeof parentWin.SillyTavern.getContext !== 'function') {
    // 在这种情况下，我们不应该抛出错误，而应该返回一个默认的、无害的上下文。
    // 因为脚本可能在SillyTavern完全加载之前运行。
    console.warn('[WIO API] SillyTavern.getContext is not available.');
    return {
      characters: [],
      characterId: null,
      chatId: null,
    };
  }
  return parentWin.SillyTavern.getContext();
};

// API封装模块
class TavernAPIWrapper {
  private helper: TavernHelper;

  constructor() {
    this.helper = getTavernHelper();
  }

  // Lorebook APIs
  createLorebook = errorCatched(async (name: string) => this.helper.createLorebook(name));
  deleteLorebook = errorCatched(async (name: string) => this.helper.deleteLorebook(name));
  getLorebooks = errorCatched(async () => this.helper.getLorebooks());
  getLorebookSettings = errorCatched(async () => this.helper.getLorebookSettings());
  setLorebookSettings = errorCatched(async (settings: any) => this.helper.setLorebookSettings(settings));

  // Lorebook Entry APIs
  getLorebookEntries = errorCatched(async (name: string) => this.helper.getLorebookEntries(name));
  setLorebookEntries = errorCatched(async (name: string, entries: any[]) =>
    this.helper.setLorebookEntries(name, entries),
  );
  createLorebookEntries = errorCatched(async (name: string, entries: any[]) =>
    this.helper.createLorebookEntries(name, entries),
  );
  deleteLorebookEntries = errorCatched(async (name: string, uids: string[]) =>
    this.helper.deleteLorebookEntries(name, uids),
  );

  // Character-specific Lorebook APIs
  getCharLorebooks = errorCatched(async (charData?: any) => this.helper.getCharLorebooks(charData));
  getCurrentCharLorebooks = errorCatched(async () => this.helper.getCharLorebooks());
  setCurrentCharLorebooks = errorCatched(async (lorebooks: string[]) => this.helper.setCurrentCharLorebooks(lorebooks));

  // Chat Lorebook APIs
  getChatLorebook = errorCatched(async () => this.helper.getChatLorebook());
  setChatLorebook = errorCatched(async (name: string | null) => this.helper.setChatLorebook(name));
  getOrCreateChatLorebook = errorCatched(async (name: string) => this.helper.getOrCreateChatLorebook(name));

  // Regex APIs
  getRegexes = errorCatched(async () => this.helper.getTavernRegexes({ scope: 'all' }));
  replaceRegexes = errorCatched(async (regexes: any[]) => this.helper.replaceTavernRegexes(regexes, { scope: 'all' }));

  // Character Data APIs
  getCharData = errorCatched(async () => this.helper.getCharData());

  // Misc APIs
  saveSettings = errorCatched(async () => this.helper.builtin.saveSettings());

  // Context API
  getContext = () => getSillyTavernContext();

  // Direct access to specific properties if needed
  get Character() {
    return this.helper.Character;
  }
}

// 导出单例
export const TavernAPI = new TavernAPIWrapper();
