# 节点 1.2: Worldbook 列表只读显示 - 开发任务清单

**负责人 (Owner)**: Developer / UX Expert
**优先级**: 高
**预估工时**: 8-12 小时
**关联文档**:

- [Roadmap - 节点 1.2](../Roadmap.md#节点-12-worldbook-列表只读显示)
- [UX-Spec - 列表页设计](../../UX-Spec.md#71-列表页-worldbook-list-view)
- [BridgeAPI - getWorldbookList()](../../BridgeAPI.md#getWorldbookList)
- [TechSpec - Vue组件架构](../../TechSpec.md#43-组件化拆分策略)

---

## 任务概述

实现 Worldbook 列表页的核心浏览功能，为用户提供清晰的 Worldbook 概览界面。**此节点只专注于只读展示，不包含编辑操作**。

**用户价值**: 用户进入应用后能立即看到所有可用的 Worldbook，并能清晰识别每个 Worldbook 的基本信息（名称、条目数）。

**验收标准**:

- [ ] 用户能看到完整的 Worldbook 列表
- [ ] 加载中、空状态、错误状态均有清晰UI展示
- [ ] 界面布局符合 UX-Spec 设计规范

---

## 详细任务清单

### 1. 【准备阶段】创建基础组件结构

#### 1.1 创建 WorldbookListView.vue 组件

- [ ] 在 `src/world_info_optimizer/views/` 下创建 `WorldbookListView.vue` 文件
- [ ] 按照 TechSpec 的组件架构，定义组件的基本结构
- [ ] 配置正确的组件名和文件命名约定
- [ ] 添加必要的 TypeScript 类型引用

#### 1.2 创建支持组件

- [ ] 创建 `WorldbookListItem.vue` 组件用于单个列表项渲染
- [ ] 创建 `ListToolbar.vue` 组件用于顶部工具栏（标签页、搜索、新建按钮）
- [ ] 确保所有组件使用 `<style scoped>` 避免样式冲突

### 2. 【数据层】实现 BridgeAPI 数据获取

#### 2.1 设置 Pinia Store 数据结构

- [ ] 在 `useWorldbookStore` 中添加 `worldbooks` 状态数组
- [ ] 定义 `WorldbookListItem` 类型引用（基于 BridgeAPI.md）
- [ ] 添加 `loading`、`error` 状态管理
- [ ] 实现 `fetchWorldbooks` action 调用 `BridgeAPI.getWorldbookList()`

#### 2.2 实现数据获取逻辑

- [ ] 在 WorldbookListView 组件中注入 `useWorldbookStore`
- [ ] 在组件挂载时 (`onMounted`) 调用 `fetchWorldbooks()`
- [ ] 正确处理异步数据加载，确保UI响应性

#### 2.3 错误处理实现

- [ ] 实现 BridgeError 捕获和处理逻辑
- [ ] 将原始错误转换为用户友好的错误信息
- [ ] 在 Store 中添加 `error` 状态，用于界面展示

### 3. 【视图层】实现列表项渲染

#### 3.1 基础列表布局

- [ ] 使用 Naive UI 的 `n-list` 或 `n-virtual-list` 组件作为基础布局
- [ ] 实现响应式布局，适配不同屏幕尺寸
- [ ] 添加适当的 padding 和 spacing

#### 3.2 Worldbook 列表项设计

- [ ] 显示 Worldbook 名称（`name` 字段）
- [ ] 显示条目总数（`entryCount` 字段）
- [ ] 实现点击导航功能（为后续节点 1.3 做准备）
- [ ] 添加合适的图标和视觉层次

#### 3.3 条件信息展示

- [ ] 对于被角色使用的 Worldbook，显示使用标识
- [ ] 实现视觉区分（如标签或图标）
- [ ] 确保信息展示遵循 UX-Spec 规范

### 4. 【状态管理】实现关键状态展示

#### 4.1 加载状态 (Loading State)

- [ ] 实现全页加载显示，使用 `n-spin` 组件
- [ ] 显示加载文案："正在加载 Worldbook 列表..."
- [ ] 禁用用户交互，直到数据加载完成

#### 4.2 空状态 (Empty State)

- [ ] 实现空状态显示，使用 `n-empty` 组件
- [ ] 显示提示文案："暂无 Worldbook，点击'新建'来创建第一个吧！"
- [ ] 提供引导性操作按钮（如跳转到创建页面）

#### 4.3 错误状态 (Error State)

- [ ] 实现错误状态显示，使用 `n-result` 组件
- [ ] 显示错误信息："数据加载失败，请检查网络连接"
- [ ] 提供"重试"按钮，允许用户重新加载
- [ ] 实现重试功能，重新调用 `fetchWorldbooks()`

### 5. 【UI优化】实现工具栏界面

#### 5.1 标签页系统 (预留)

- [ ] 创建标签页结构（全局、角色、聊天）
- [ ] 设置默认激活"全局"标签
- [ ] 为后续多标签支持打下基础

#### 5.2 搜索框 (预留)

- [ ] 添加搜索框占位符，暂时禁用
- [ ] 按照 UX-Spec 布局设计
- [ ] 为后续搜索功能预留接口

#### 5.3 工具按钮 (预留)

- [ ] 添加"刷新"按钮图标
- [ ] 添加"新建Worldbook"按钮，暂时禁用
- [ ] 确保按钮布局符合设计规范

### 6. 【集成与调试】与主应用集成

#### 6.1 路由配置

- [ ] 确保 WorldbookListView 能正确挂载到主应用中
- [ ] 测试组件间的通信是否正常
- [ ] 验证数据流从 BridgeAPI -> Store -> View

#### 6.2 样式调试

- [ ] 验证样式是否与 SillyTavern 主题兼容
- [ ] 使用浏览器开发者工具检查布局
- [ ] 确保在不同分辨率下显示正常

#### 6.3 功能测试

- [ ] 测试正常数据加载流程
- [ ] 测试网络错误场景
- [ ] 测试空数据场景
- [ ] 验证加载状态提示正确显示

### 7. 【验收与文档】

#### 7.1 功能验收

- [ ] 邀请 PO 和 UX Expert 进行功能验收
- [ ] 验证所有验收标准是否满足
- [ ] 收集反馈并制定改进计划

#### 7.2 代码审查

- [ ] 确保代码符合 TechSpec 的架构规范
- [ ] 验证 TypeScript 类型定义完整
- [ ] 检查代码注释和文档字符串

---

## 技术风险评估

| 风险等级 | 风险描述             | 缓解策略                                |
| -------- | -------------------- | --------------------------------------- |
| 中等     | BridgeAPI 调用失败   | 实现完善的错误处理和用户反馈            |
| 低       | 样式冲突             | 使用 scoped 样式和父窗口样式隔离        |
| 低       | 性能问题（大数据量） | 先实现基础版本，后续优化考虑虚拟滚动    |
| 低       | 数据格式不匹配       | 与 BridgeAPI 文档保持同步，编写单元测试 |

---

## 依赖关系

**前置依赖**:

- ✅ 节点 1.1: UI 入口与 DOM 注入 (已完成)
- ✅ BridgeAPI.getWorldbookList() 方法 (已实现)

**后续影响**:

- 🔄 节点 1.3: 导航至详情页 (将使用此节点的导航功能)
- 🔄 节点 2.1+: 编辑功能都依赖此节点的列表展示

---

## 质量保证

- [ ] 单元测试: `useWorldbookStore` 的 `fetchWorldbooks` 方法
- [ ] 组件测试: `WorldbookListView` 的状态展示
- [ ] E2E 测试: 完整的列表加载和状态展示流程
- [ ] 性能测试: 数据加载时间不超过 2 秒
- [ ] 兼容性测试: 在不同浏览器和 SillyTavern 版本下正常工作
