// src/world_info_optimizer/ui.ts

import {
  BUTTON_ICON_URL,
  BUTTON_ID,
  BUTTON_TEXT_IN_MENU,
  COLLAPSE_CURRENT_BTN_ID,
  CREATE_LOREBOOK_BTN_ID,
  PANEL_ID,
  REFRESH_BTN_ID,
  REFRESH_CHARACTER_BTN_ID,
  SEARCH_INPUT_ID,
} from './constants';
import { getState, subscribe } from './store';
import {
  renderCharacterLorebookView,
  renderChatLorebookView,
  renderGlobalLorebookView,
  renderRegexView,
} from './ui/views';

// --- Private Variables ---
let parentDoc: Document;
let $: JQueryStatic;

// --- Main Panel and Button Injection ---

export const injectUI = (parentWindow: Window) => {
  parentDoc = parentWindow.document;
  $ = parentWindow.jQuery;

  if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
    console.log('[WIO] UI already injected.');
    return;
  }

  const styles = `
        :root {
            /* 紧凑布局 */
            --wio-font-size-sm: 11px;
            --wio-font-size-md: 13px;
            --wio-font-size-lg: 15px;
            --wio-spacing-xs: 2px;
            --wio-spacing-sm: 6px;
            --wio-spacing-md: 10px;
            --wio-spacing-lg: 14px;

            /* 扁平化风格 */
            --wio-border-radius: 4px;
            --wio-shadow: none;
            
            /* 暗色主题 */
            --wio-bg-primary: #1f1f1f;
            --wio-bg-secondary: #2d2d2d;
            --wio-bg-tertiary: #3c3c3c;
            --wio-bg-toolbar: #252525;
            --wio-text-primary: #e0e0e0;
            --wio-text-secondary: #9e9e9e;
            --wio-highlight-color: #29b6f6;
            --wio-border-color: #424242;
        }

        /* All WIO styles here... */
        #${PANEL_ID} {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 10000;
            overflow: hidden;
        }
        
        #${PANEL_ID} .wio-panel-inner {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            background-color: var(--wio-bg-primary);
            color: var(--wio-text-primary);
        }


        /* 头部样式 */
        .wio-header {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: var(--wio-bg-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--wio-border-color);
            flex-shrink: 0;
        }
        
        .wio-header h2 {
            margin: 0;
            font-size: var(--wio-font-size-lg);
        }
        
        .wio-close-btn {
            background: none;
            border: none;
            color: var(--wio-text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: var(--wio-spacing-xs);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: color 0.2s ease;
        }
        
        .wio-close-btn:hover {
            color: var(--wio-text-primary);
        }
        
        /* 选项卡样式 */
        .wio-tabs {
            display: flex;
            background-color: var(--wio-bg-tertiary);
            overflow-x: auto;
            white-space: nowrap;
            flex-shrink: 0;
            border-bottom: 1px solid var(--wio-border-color);
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .wio-tabs::-webkit-scrollbar {
            display: none;
        }
        
        .wio-tab-btn {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            border: none;
            background-color: transparent;
            color: var(--wio-text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            flex-shrink: 0;
            font-size: var(--wio-font-size-md);
            transition: all 0.2s ease;
            outline: none;
        }
        
        .wio-tab-btn.active {
            color: var(--wio-highlight-color);
            border-bottom-color: var(--wio-highlight-color);
            background-color: rgba(41, 182, 246, 0.15);
        }
        
        .wio-tab-btn:focus {
            outline: 2px solid var(--wio-highlight-color);
            outline-offset: 2px;
        }
        
        /* 工具栏样式 */
        .wio-toolbar {
            padding: var(--wio-spacing-md);
            display: flex;
            gap: var(--wio-spacing-md);
            background-color: var(--wio-bg-toolbar);
            border-bottom: 1px solid var(--wio-border-color);
            flex-shrink: 0;
        }
        
        #${SEARCH_INPUT_ID} {
            flex-grow: 1;
            padding: var(--wio-spacing-md);
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            background-color: var(--wio-bg-secondary); /* Darker background */
            color: var(--wio-text-primary);
            font-size: var(--wio-font-size-md);
            outline: none;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        #${SEARCH_INPUT_ID}::placeholder {
            color: var(--wio-text-secondary);
        }
        
        #${SEARCH_INPUT_ID}:focus {
            border-color: var(--wio-highlight-color);
            box-shadow: none;
        }
        
        .wio-toolbar button {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            border: none;
            border-radius: var(--wio-border-radius);
            background-color: transparent;
            color: var(--wio-text-secondary);
            cursor: pointer;
            font-size: var(--wio-font-size-md);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--wio-spacing-xs);
            transition: all 0.2s ease;
            outline: none;
            min-width: 40px;
            min-height: 40px;
        }
        
        .wio-toolbar button:hover {
            background-color: var(--wio-bg-tertiary);
            color: var(--wio-text-primary);
        }
        
        .wio-toolbar button:focus {
            outline: 2px solid var(--wio-highlight-color);
            outline-offset: 2px;
        }

        /* 主内容区域样式 */
        .wio-main-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: var(--wio-spacing-lg);
        }
        
        /* 列表样式 */
        .wio-book-group {
            margin-bottom: var(--wio-spacing-lg);
            border-radius: var(--wio-border-radius);
            overflow: hidden;
            background-color: var(--wio-bg-secondary);
        }
        
        .wio-book-header {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: var(--wio-bg-tertiary);
            display: flex;
            align-items: center;
            gap: var(--wio-spacing-md);
        }
        
        .wio-book-header h4 {
            margin: 0;
            flex-grow: 1;
            font-size: var(--wio-font-size-lg);
        }
        
        .wio-usage-pill {
            padding: 2px 8px;
            background-color: var(--wio-highlight-color);
            color: #1f1f1f;
            border-radius: 12px;
            font-size: var(--wio-font-size-sm);
            font-weight: bold;
        }
        
        .wio-item-controls {
            display: flex;
            gap: var(--wio-spacing-xs);
        }
        
        .wio-item-controls button {
            padding: var(--wio-spacing-xs);
            background-color: transparent;
            border: none;
            border-radius: var(--wio-border-radius);
            color: var(--wio-text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        
        .wio-item-controls button:hover {
            background-color: var(--wio-bg-tertiary);
            color: var(--wio-text-primary);
        }
        
        .wio-entry-list {
            background-color: var(--wio-bg-secondary);
        }
        
        .wio-entry-item {
            padding: var(--wio-spacing-md);
            border-bottom: 1px solid var(--wio-bg-tertiary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .wio-entry-item:last-child {
            border-bottom: none;
        }
        
        .wio-entry-main {
            display: flex;
            align-items: center;
            gap: var(--wio-spacing-md);
            flex-grow: 1;
        }
        
        .wio-entry-name {
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .wio-entry-keys {
            font-size: var(--wio-font-size-sm);
            color: var(--wio-text-secondary);
            font-style: italic;
            flex-grow: 1;
            word-break: break-word;
        }
        
        .wio-entry-actions,
        .wio-regex-actions {
            padding: var(--wio-spacing-md);
            text-align: center;
            background-color: var(--wio-bg-tertiary);
        }
        
        .wio-create-entry-btn,
        .wio-create-regex-btn {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: var(--wio-highlight-color);
            border: none;
            border-radius: var(--wio-border-radius);
            color: #ffffff;
            cursor: pointer;
            font-size: var(--wio-font-size-md);
            transition: background-color 0.2s ease;
        }

        .wio-create-entry-btn:hover,
        .wio-create-regex-btn:hover {
            background-color: #0091ea; /* A slightly darker shade of the highlight color */
        }

        /* 正则表达式样式 */
        .wio-regex-group {
            margin-bottom: var(--wio-spacing-lg);
            border-radius: var(--wio-border-radius);
            overflow: hidden;
            background-color: var(--wio-bg-secondary);
        }
        
        .wio-regex-group h3 {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            margin: 0;
            background-color: var(--wio-bg-tertiary);
        }
        
        .wio-regex-item {
            padding: var(--wio-spacing-md);
            border-bottom: 1px solid var(--wio-bg-tertiary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .wio-regex-item:last-child {
            border-bottom: none;
        }
        
        .wio-regex-main {
            display: flex;
            align-items: center;
            gap: var(--wio-spacing-md);
            flex-grow: 1;
            flex-wrap: wrap;
        }
        
        .wio-regex-name {
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .wio-regex-find,
        .wio-regex-replace {
            background-color: var(--wio-bg-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: var(--wio-font-size-sm);
            word-break: break-all;
            border: 1px solid var(--wio-border-color);
        }
        
        .wio-info-text {
            text-align: center;
            color: var(--wio-text-secondary);
            font-style: italic;
            padding: var(--wio-spacing-lg);
        }

        .wio-error-container {
            text-align: center;
            padding: var(--wio-spacing-lg);
        }

        .wio-error-text {
            color: #ff6b6b;
            margin-bottom: var(--wio-spacing-md);
            font-weight: bold;
        }

        .wio-retry-btn {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: #c62828;
            border: none;
            border-radius: var(--wio-border-radius);
            color: #ffffff;
            cursor: pointer;
            font-size: var(--wio-font-size-md);
            transition: background-color 0.2s ease;
        }

        .wio-retry-btn:hover {
            background-color: #b71c1c;
        }
        
        .wio-search-highlight {
            background-color: rgba(255, 255, 0, 0.3);
            padding: 0 2px;
            border-radius: 2px;
        }

        /* 页脚样式 */
        .wio-footer {
            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);
            background-color: var(--wio-bg-tertiary);
            font-size: var(--wio-font-size-sm);
            text-align: right;
            border-top: 1px solid var(--wio-border-color);
            flex-shrink: 0;
        }

        /* SweetAlert2 输入框样式覆盖 */
        .swal2-input, .swal2-textarea {
            background-color: var(--wio-bg-secondary) !important;
            color: var(--wio-text-primary) !important;
            border: 1px solid var(--wio-border-color) !important;
        }
        
        .swal2-input:focus, .swal2-textarea:focus {
            border-color: var(--wio-highlight-color) !important;
            box-shadow: none !important;
        }

        /* SweetAlert2 模态框样式覆盖 */
        .swal2-popup {
            background-color: var(--wio-bg-primary) !important;
            color: var(--wio-text-primary) !important;
        }

        .swal2-title {
            color: var(--wio-text-primary) !important;
        }

        .swal2-html-container {
            color: var(--wio-text-secondary) !important;
        }

        .swal2-confirm, .swal2-cancel {
            border-radius: var(--wio-border-radius) !important;
            transition: background-color 0.2s ease;
        }

        .swal2-confirm {
            background-color: var(--wio-highlight-color) !important;
        }
        
        .swal2-cancel {
            background-color: var(--wio-bg-tertiary) !important;
        }
        
        .swal2-toast {
             background-color: var(--wio-bg-secondary) !important;
             color: var(--wio-text-primary) !important;
        }

        /* 复选框样式优化 */
        input[type="checkbox"] {
            transform: scale(1.1);
            accent-color: var(--wio-highlight-color);
            margin-right: var(--wio-spacing-sm);
            background-color: var(--wio-bg-secondary);
            border: 1px solid var(--wio-border-color);
            border-radius: 2px;
            appearance: none;
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            cursor: pointer;
            position: relative;
            top: 2px;
        }

        input[type="checkbox"]:checked {
            background-color: var(--wio-highlight-color);
            border-color: var(--wio-highlight-color);
        }

        input[type="checkbox"]:checked::after {
            content: '✔';
            position: absolute;
            color: #1f1f1f;
            font-size: 12px;
            top: -1px;
            left: 2px;
        }

        /* 折叠按钮样式 */
        .wio-collapse-toggle {
            background: none;
            border: none;
            color: var(--wio-text-secondary);
            cursor: pointer;
            padding: var(--wio-spacing-xs);
            margin-right: var(--wio-spacing-sm);
            font-size: 14px;
            transition: color 0.2s ease;
            min-width: 20px;
            text-align: center;
        }

        .wio-collapse-toggle:hover {
            color: var(--wio-text-primary);
        }

        /* 多选复选框样式 */
        .wio-multi-select-checkbox {
            margin-right: var(--wio-spacing-sm);
            border: 2px solid var(--wio-highlight-color) !important;
        }

        .wio-multi-select-checkbox:checked {
            background-color: var(--wio-highlight-color) !important;
        }

        /* 工具栏按钮激活状态 */
        .wio-toolbar button.active {
            background-color: var(--wio-highlight-color);
            color: #ffffff;
        }

        /* 批量操作输入框样式 */
        #wio-bulk-search-input,
        #wio-bulk-replace-input {
            flex-grow: 1;
            max-width: 200px;
            padding: var(--wio-spacing-sm);
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            background-color: var(--wio-bg-secondary);
            color: var(--wio-text-primary);
            font-size: var(--wio-font-size-sm);
            outline: none;
            transition: border-color 0.2s ease;
        }

        #wio-bulk-search-input:focus,
        #wio-bulk-replace-input:focus {
            border-color: var(--wio-highlight-color);
        }

        #wio-bulk-search-input::placeholder,
        #wio-bulk-replace-input::placeholder {
            color: var(--wio-text-secondary);
        }

        /* 拖拽排序样式 */
        .wio-regex-draggable {
            cursor: move;
            transition: all 0.2s ease;
        }

        .wio-regex-draggable:hover {
            background-color: var(--wio-bg-tertiary);
        }

        .wio-drag-handle {
            color: var(--wio-text-secondary);
            cursor: grab;
            margin-right: var(--wio-spacing-sm);
            font-size: 12px;
            opacity: 0.6;
            transition: opacity 0.2s ease;
        }

        .wio-drag-handle:hover {
            opacity: 1;
            color: var(--wio-text-primary);
        }

        .wio-regex-draggable:active .wio-drag-handle {
            cursor: grabbing;
        }

        .wio-regex-item.wio-dragging {
            opacity: 0.5;
            transform: rotate(2deg);
            z-index: 1000;
        }

        .wio-regex-list {
            position: relative;
        }

        .wio-drop-indicator {
            height: 2px;
            background-color: var(--wio-highlight-color);
            margin: 2px 0;
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .wio-drop-indicator.active {
            opacity: 1;
        }

        /* 聚焦样式优化 */
        :focus-visible {
            outline: 2px solid var(--wio-highlight-color);
            outline-offset: 2px;
        }

        /* 滚动条样式 */
        .wio-main-content::-webkit-scrollbar,
        .wio-tabs::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .wio-main-content::-webkit-scrollbar-track,
        .wio-tabs::-webkit-scrollbar-track {
            background: var(--wio-bg-tertiary);
        }
        
        .wio-main-content::-webkit-scrollbar-thumb,
        .wio-tabs::-webkit-scrollbar-thumb {
            background: var(--wio-border-color);
            border-radius: 4px;
        }
        
        .wio-main-content::-webkit-scrollbar-thumb:hover,
        .wio-tabs::-webkit-scrollbar-thumb:hover {
            background: #777;
        }

        /* 媒体查询：小型设备优化 */
        @media (max-width: 767px) {
            :root {
                --wio-spacing-xs: 2px;
                --wio-spacing-sm: 6px;
                --wio-spacing-md: 8px;
                --wio-spacing-lg: 10px;
                --wio-font-size-sm: 11px;
                --wio-font-size-md: 13px;
                --wio-font-size-lg: 15px;
            }
            
            .wio-header h2 {
                font-size: var(--wio-font-size-md);
            }
            
            .wio-entry-main {
                flex-wrap: wrap;
            }
            
            .wio-entry-name {
                flex-basis: 100%;
                margin-bottom: var(--wio-spacing-xs);
            }
            
            .wio-regex-main {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--wio-spacing-xs);
            }
            
            .wio-regex-name,
            .wio-regex-find,
            .wio-regex-replace {
                width: 100%;
                box-sizing: border-box;
            }
            
            .wio-toolbar {
                flex-wrap: wrap;
                gap: var(--wio-spacing-sm); /* 缩小gap */
            }
            
            #${SEARCH_INPUT_ID} {
                flex-basis: 100%; /* 确保搜索框始终占满一行 */
            }
            
            .wio-toolbar button {
                flex-grow: 1; /* 让按钮平均分配剩余空间 */
                min-width: 44px; /* 保证最小触摸尺寸 */
            }

            .wio-tabs {
                position: relative; /* 为伪元素定位 */
            }

            .wio-tabs::before,
            .wio-tabs::after {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                width: 20px; /* 渐变宽度 */
                pointer-events: none; /* 允许点击穿透 */
            }

            .wio-tabs::before {
                left: 0;
                background: linear-gradient(to right, var(--wio-bg-tertiary), transparent);
            }

            .wio-tabs::after {
                right: 0;
                background: linear-gradient(to left, var(--wio-bg-tertiary), transparent);
            }

            .wio-main-content {
                padding: var(--wio-spacing-md); /* 统一内边距 */
            }

            .wio-entry-keys {
                font-size: var(--wio-font-size-sm);
                white-space: normal; /* 允许长文本换行 */
                word-break: break-all;
            }
            
            /* 触摸目标优化 */
            button,
            input[type="checkbox"] {
                touch-action: manipulation;
            }
        }
        
        /* 平板设备优化 */
        @media (min-width: 768px) and (max-width: 1024px) {
            #${PANEL_ID} {
                width: 90%;
                height: 80%;
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            :root {
                --wio-border-color: #fff !important;
                --wio-bg-primary: #000 !important;
                --wio-bg-secondary: #333 !important;
                --wio-bg-tertiary: #222 !important;
                --wio-bg-toolbar: #444 !important;
                --wio-text-primary: #fff !important;
                --wio-text-secondary: #ddd !important;
                --wio-highlight-color: #ff0 !important;
            }
        }


        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    `;
  const styleSheet = parentDoc.createElement('style');
  styleSheet.innerText = styles;
  parentDoc.head.appendChild(styleSheet);

  const panelHtml = `
        <div id="${PANEL_ID}">
            <div class="wio-panel-inner">
                <div class="wio-header">
                    <h2>世界书 & 正则便捷管理 (WIO)</h2>
                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>
                </div>
                <div class="wio-tabs" role="tablist">
                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>
                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>
                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>
                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>
                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>
                </div>
                <div class="wio-toolbar">
                    <input type="search" id="${SEARCH_INPUT_ID}" placeholder="搜索..." aria-label="搜索内容">
                    <input type="text" id="wio-bulk-search-input" placeholder="批量搜索..." aria-label="批量搜索内容" style="display: none;">
                    <input type="text" id="wio-bulk-replace-input" placeholder="替换为..." aria-label="替换内容" style="display: none;">
                    <button id="wio-bulk-replace-btn" title="批量替换" aria-label="批量替换" style="display: none;"><i class="fa-solid fa-exchange-alt"></i></button>
                    <button id="wio-multi-select-btn" title="多选模式" aria-label="切换多选模式"><i class="fa-solid fa-check-square"></i></button>
                    <button id="wio-bulk-delete-btn" title="批量删除" aria-label="批量删除选中项" style="display: none;"><i class="fa-solid fa-trash"></i></button>
                    <button id="wio-bulk-enable-btn" title="批量启用" aria-label="批量启用选中项" style="display: none;"><i class="fa-solid fa-eye"></i></button>
                    <button id="wio-bulk-disable-btn" title="批量禁用" aria-label="批量禁用选中项" style="display: none;"><i class="fa-solid fa-eye-slash"></i></button>
                    <button id="${COLLAPSE_CURRENT_BTN_ID}" title="折叠当前角色" aria-label="折叠当前角色的世界书"><i class="fa-solid fa-user-minus"></i></button>
                    <button id="wio-collapse-all-btn" title="全部折叠" aria-label="折叠所有世界书"><i class="fa-solid fa-compress"></i></button>
                    <button id="wio-expand-all-btn" title="全部展开" aria-label="展开所有世界书"><i class="fa-solid fa-expand"></i></button>
                    <button id="${REFRESH_CHARACTER_BTN_ID}" title="刷新角色数据" aria-label="刷新角色数据"><i class="fa-solid fa-user-sync"></i></button>
                    <button id="${REFRESH_BTN_ID}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>
                    <button id="${CREATE_LOREBOOK_BTN_ID}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>
                </div>
                <div class="wio-main-content" role="main" id="tab-content-container"></div>
                <div class="wio-footer">
                    <span>WIO v3.0 (Refactored)</span>
                </div>
            </div>
        </div>
    `;
  $('body', parentDoc).append(panelHtml);

  const extensionButton = `
        <div id="${BUTTON_ID}" class="list-group-item">
            <img src="${BUTTON_ICON_URL}" style="width: 24px; height: 24px; margin-right: 10px;">
            <span>${BUTTON_TEXT_IN_MENU}</span>
        </div>
    `;
  $('#extensionsMenu', parentDoc).append(extensionButton);

  console.log('[WIO] UI Injected successfully.');
};

// --- Helper Functions ---

/**
 * 更新工具栏按钮的显示状态
 */
const updateToolbarButtons = (state: any) => {
  if (!parentDoc) return;

  const isLorebookTab = ['global-lore', 'char-lore', 'chat-lore'].includes(state.activeTab);
  const hasSelectedItems = state.selectedItems.size > 0;

  // 批量搜索替换相关按钮 - 只在世界书标签页显示
  const $bulkSearchInput = $(`#wio-bulk-search-input`, parentDoc);
  const $bulkReplaceInput = $(`#wio-bulk-replace-input`, parentDoc);
  const $bulkReplaceBtn = $(`#wio-bulk-replace-btn`, parentDoc);

  if (isLorebookTab && !state.multiSelectMode) {
    $bulkSearchInput.show();
    $bulkReplaceInput.show();
    $bulkReplaceBtn.show();
  } else {
    $bulkSearchInput.hide();
    $bulkReplaceInput.hide();
    $bulkReplaceBtn.hide();
  }

  // 多选模式按钮 - 只在世界书标签页显示
  const $multiSelectBtn = $(`#wio-multi-select-btn`, parentDoc);
  if (isLorebookTab) {
    $multiSelectBtn.show();
    $multiSelectBtn.toggleClass('active', state.multiSelectMode);
  } else {
    $multiSelectBtn.hide();
  }

  // 批量操作按钮 - 只在多选模式下显示
  const $bulkDeleteBtn = $(`#wio-bulk-delete-btn`, parentDoc);
  const $bulkEnableBtn = $(`#wio-bulk-enable-btn`, parentDoc);
  const $bulkDisableBtn = $(`#wio-bulk-disable-btn`, parentDoc);

  if (state.multiSelectMode && isLorebookTab) {
    $bulkDeleteBtn.show();
    $bulkEnableBtn.show();
    $bulkDisableBtn.show();

    // 根据是否有选中项来启用/禁用按钮
    $bulkDeleteBtn.prop('disabled', !hasSelectedItems);
    $bulkEnableBtn.prop('disabled', !hasSelectedItems);
    $bulkDisableBtn.prop('disabled', !hasSelectedItems);
  } else {
    $bulkDeleteBtn.hide();
    $bulkEnableBtn.hide();
    $bulkDisableBtn.hide();
  }

  // 折叠/展开按钮 - 只在世界书标签页显示
  const $collapseCurrentBtn = $(`#${COLLAPSE_CURRENT_BTN_ID}`, parentDoc);
  const $collapseAllBtn = $(`#wio-collapse-all-btn`, parentDoc);
  const $expandAllBtn = $(`#wio-expand-all-btn`, parentDoc);

  const isCharacterSpecificTab = ['char-lore', 'chat-lore'].includes(state.activeTab);

  if (isLorebookTab) {
    // “折叠当前”按钮只在角色和聊天世界书标签页有意义
    if (isCharacterSpecificTab) {
      $collapseCurrentBtn.show();
    } else {
      $collapseCurrentBtn.hide();
    }
    $collapseAllBtn.show();
    $expandAllBtn.show();
  } else {
    $collapseCurrentBtn.hide();
    $collapseAllBtn.hide();
    $expandAllBtn.hide();
  }

  // 角色刷新按钮 - 只在角色相关标签页显示
  const $refreshCharacterBtn = $(`#${REFRESH_CHARACTER_BTN_ID}`, parentDoc);
  const isCharacterTab = ['char-lore', 'chat-lore', 'char-regex'].includes(state.activeTab);

  if (isCharacterTab) {
    $refreshCharacterBtn.show();
  } else {
    $refreshCharacterBtn.hide();
  }
};

// --- Core Render Logic ---

const render = () => {
  if (!parentDoc) return;

  const state = getState();
  const $panel = $(`#${PANEL_ID}`, parentDoc);
  if (!$panel.length) return;

  $panel.find('.wio-tab-btn').removeClass('active');
  $panel.find(`.wio-tab-btn[data-tab-id="${state.activeTab}"]`).addClass('active');

  // 更新工具栏按钮状态
  updateToolbarButtons(state);

  const $mainContent = $panel.find('.wio-main-content');

  // 处理加载状态
  if (state.isLoading) {
    $mainContent.html('<p class="wio-info-text">正在加载数据...</p>');
    return;
  }

  // 处理错误状态
  if (state.loadError) {
    $mainContent.html(`
      <div class="wio-error-container">
        <p class="wio-error-text">${state.loadError}</p>
        <button id="${REFRESH_BTN_ID}-retry" class="wio-retry-btn">重试</button>
      </div>
    `);
    return;
  }

  // 处理未加载状态
  if (!state.isDataLoaded) {
    $mainContent.html('<p class="wio-info-text">点击刷新按钮加载数据</p>');
    return;
  }

  const searchTerm = state.searchQuery.toLowerCase();
  const $searchInput = $(`#${SEARCH_INPUT_ID}`, parentDoc);
  if ($searchInput.val() !== state.searchQuery) {
    $searchInput.val(state.searchQuery);
  }

  switch (state.activeTab) {
    case 'global-lore':
      $mainContent.html(renderGlobalLorebookView(state, searchTerm));
      break;
    case 'char-lore':
      $mainContent.html(renderCharacterLorebookView(state, searchTerm));
      break;
    case 'chat-lore':
      $mainContent.html(renderChatLorebookView(state, searchTerm));
      break;
    case 'global-regex':
      $mainContent.html(renderRegexView(state.regexes.global, searchTerm, '全局正则', 'global'));
      break;
    case 'char-regex':
      $mainContent.html(renderRegexView(state.regexes.character, searchTerm, '角色正则', 'character'));
      break;
    default:
      $mainContent.html(`<p>未知视图: ${state.activeTab}</p>`);
  }
};

// --- UI Initialization ---

export const initUI = () => {
  subscribe(render);
};
