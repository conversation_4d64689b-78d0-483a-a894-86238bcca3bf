// src/world_info_optimizer/ui/views.ts

import { getState } from '../store';
import { AppState, TavernRegex } from '../types';
import { createLorebookElement, createRegexItemElement } from './elements';
import { escapeHtml } from './helpers';

export const renderGlobalLorebookView = (state: AppState, searchTerm: string): string => {
  const books = [...state.allLorebooks].sort(
    (a, b) => (b.enabled ? 1 : -1) - (a.enabled ? 1 : -1) || a.name.localeCompare(b.name),
  );
  if (books.length === 0) return `<p class="wio-info-text">没有找到全局世界书。</p>`;

  if (!searchTerm) {
    return books.map(book => createLorebookElement(book, state, searchTerm)).join('');
  }

  const filteredBookHtml = books
    .map(book => {
      const bookNameMatches = book.name.toLowerCase().includes(searchTerm);
      const entries = state.lorebookEntries.get(book.name) || [];

      const filteredEntries = entries.filter(
        entry =>
          (entry.comment || '').toLowerCase().includes(searchTerm) ||
          (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||
          (entry.content || '').toLowerCase().includes(searchTerm),
      );

      if (bookNameMatches || filteredEntries.length > 0) {
        const entriesToShow = bookNameMatches ? entries : filteredEntries;
        return createLorebookElement(book, state, searchTerm, true, entriesToShow);
      }
      return '';
    })
    .join('');

  return filteredBookHtml || `<p class="wio-info-text">没有找到与 "${escapeHtml(searchTerm)}" 匹配的结果。</p>`;
};

export const renderCharacterLorebookView = (state: AppState, searchTerm: string): string => {
  const linkedBooks = state.lorebooks.character;
  if (linkedBooks.length === 0) return `<p class="wio-info-text">当前角色没有绑定的世界书。</p>`;

  if (!searchTerm) {
    return linkedBooks
      .map(bookName => {
        const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };
        return createLorebookElement(bookFile, state, searchTerm, false);
      })
      .join('');
  }

  const filteredBookHtml = linkedBooks
    .map(bookName => {
      const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };
      const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);
      const entries = state.lorebookEntries.get(bookFile.name) || [];

      const filteredEntries = entries.filter(
        entry =>
          (entry.comment || '').toLowerCase().includes(searchTerm) ||
          (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||
          (entry.content || '').toLowerCase().includes(searchTerm),
      );

      if (bookNameMatches || filteredEntries.length > 0) {
        const entriesToShow = bookNameMatches ? entries : filteredEntries;
        return createLorebookElement(bookFile, state, searchTerm, false, entriesToShow);
      }
      return '';
    })
    .join('');

  return filteredBookHtml || `<p class="wio-info-text">没有找到与 "${escapeHtml(searchTerm)}" 匹配的结果。</p>`;
};

export const renderChatLorebookView = (state: AppState, searchTerm: string): string => {
  const bookName = state.chatLorebook;
  if (!bookName) return `<p class="wio-info-text">当前聊天没有绑定的世界书。</p>`;

  const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };

  if (!searchTerm) {
    return createLorebookElement(bookFile, state, searchTerm, false);
  }

  const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);
  const entries = state.lorebookEntries.get(bookFile.name) || [];

  const filteredEntries = entries.filter(
    entry =>
      (entry.comment || '').toLowerCase().includes(searchTerm) ||
      (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||
      (entry.content || '').toLowerCase().includes(searchTerm),
  );

  if (bookNameMatches || filteredEntries.length > 0) {
    const entriesToShow = bookNameMatches ? entries : filteredEntries;
    return createLorebookElement(bookFile, state, searchTerm, false, entriesToShow);
  }

  return `<p class="wio-info-text">没有找到与 "${escapeHtml(searchTerm)}" 匹配的结果。</p>`;
};

export const renderRegexView = (
  regexes: TavernRegex[],
  searchTerm: string,
  title: string,
  scope: 'global' | 'character',
): string => {
  let content = `<div class="wio-regex-group" data-scope="${scope}"><h3>${title} (${regexes.length})</h3>`;
  if (regexes.length === 0 && scope === 'character' && !getState().character) {
    content += `<p class="wio-info-text">没有加载角色，无法显示角色正则。</p>`;
  } else if (regexes.length === 0) {
    content += `<p class="wio-info-text">没有找到正则。</p>`;
  }

  const filteredRegexes = searchTerm
    ? regexes.filter(
        r =>
          (r.script_name || '').toLowerCase().includes(searchTerm) ||
          (r.find_regex || '').toLowerCase().includes(searchTerm) ||
          (r.replace_string || '').toLowerCase().includes(searchTerm),
      )
    : regexes;

  if (filteredRegexes.length > 0) {
    // 添加拖拽容器，用于拖拽排序
    content += `<div class="wio-regex-list" data-scope="${scope}">`;
    content += filteredRegexes.map(r => createRegexItemElement(r, searchTerm)).join('');
    content += '</div>';
  } else if (searchTerm) {
    content += `<p class="wio-info-text">没有找到与 "${escapeHtml(searchTerm)}" 匹配的正则。</p>`;
  }

  if (scope === 'global' || (scope === 'character' && getState().character)) {
    content += `<div class="wio-regex-actions"><button class="wio-create-regex-btn" data-scope="${scope}">+ 新建正则</button></div>`;
  }

  return content + '</div>';
};
