// src/world_info_optimizer/ui/helpers.ts

export const escapeHtml = (text: any): string => {
  if (typeof text !== 'string') text = String(text);
  const p = document.createElement('p');
  p.textContent = text;
  return p.innerHTML;
};

export const highlightText = (text: string, searchTerm: string): string => {
  if (!searchTerm || !text) return escapeHtml(text);
  const escapedText = escapeHtml(text);
  const htmlSafeSearchTerm = escapeHtml(searchTerm);
  const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');
  return escapedText.replace(regex, '<mark class="wio-highlight">$1</mark>');
};
